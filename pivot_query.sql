-- 原始查询优化版本 - 将dict_label作为列展示
WITH time_series_data AS (
    -- 使用UNION ALL合并所有时间点的数据
    SELECT curve_date as data_time, '00:15' as time, curve_type, sum(p1) as energy 
    FROM dwd_spot_trade_element_curve 
    WHERE curve_date = '2024-03-01' 
      AND unit_code = '3377981199799549979' 
      AND curve_type IN ('sbcq_cq','ssjh_cq','yphjh_cq','cdqyc_cq','dqyc_cq','kkxjh_cq','scada_cq','rqdj_cq','ssdj_cq') 
    GROUP BY curve_type, curve_date
    
    UNION ALL
    
    SELECT curve_date as data_time, '00:30' as time, curve_type, sum(p2) as energy 
    FROM dwd_spot_trade_element_curve 
    WHERE curve_date = '2024-03-01' 
      AND unit_code = '3377981199799549979' 
      AND curve_type IN ('sbcq_cq','ssjh_cq','yphjh_cq','cdqyc_cq','dqyc_cq','kkxjh_cq','scada_cq','rqdj_cq','ssdj_cq') 
    GROUP BY curve_type, curve_date
    
    UNION ALL
    
    SELECT curve_date as data_time, '00:45' as time, curve_type, sum(p3) as energy 
    FROM dwd_spot_trade_element_curve 
    WHERE curve_date = '2024-03-01' 
      AND unit_code = '3377981199799549979' 
      AND curve_type IN ('sbcq_cq','ssjh_cq','yphjh_cq','cdqyc_cq','dqyc_cq','kkxjh_cq','scada_cq','rqdj_cq','ssdj_cq') 
    GROUP BY curve_type, curve_date
    
    UNION ALL
    
    SELECT curve_date as data_time, '01:00' as time, curve_type, sum(p4) as energy 
    FROM dwd_spot_trade_element_curve 
    WHERE curve_date = '2024-03-01' 
      AND unit_code = '3377981199799549979' 
      AND curve_type IN ('sbcq_cq','ssjh_cq','yphjh_cq','cdqyc_cq','dqyc_cq','kkxjh_cq','scada_cq','rqdj_cq','ssdj_cq') 
    GROUP BY curve_type, curve_date
),
joined_data AS (
    -- 关联字典表获取标签
    SELECT 
        a.data_time,
        a.time,
        a.curve_type,
        COALESCE(b.dict_label, a.curve_type) as dict_label,
        a.energy
    FROM time_series_data a 
    LEFT JOIN sys_dict_code b ON a.curve_type = b.dict_code
)

-- 方案1: 使用CASE WHEN进行数据透视（适用于大多数数据库）
SELECT 
    data_time,
    time,
    SUM(CASE WHEN dict_label = '申报出清' THEN energy END) as 申报出清,
    SUM(CASE WHEN dict_label = '实时计划' THEN energy END) as 实时计划,
    SUM(CASE WHEN dict_label = '预排计划' THEN energy END) as 预排计划,
    SUM(CASE WHEN dict_label = '超短期预测' THEN energy END) as 超短期预测,
    SUM(CASE WHEN dict_label = '短期预测' THEN energy END) as 短期预测,
    SUM(CASE WHEN dict_label = '开口小计划' THEN energy END) as 开口小计划,
    SUM(CASE WHEN dict_label = 'SCADA出清' THEN energy END) as SCADA出清,
    SUM(CASE WHEN dict_label = '容量电价' THEN energy END) as 容量电价,
    SUM(CASE WHEN dict_label = '实时电价' THEN energy END) as 实时电价
FROM joined_data
GROUP BY data_time, time
ORDER BY data_time, time;

-- 方案2: 使用PIVOT语法（适用于SQL Server、Oracle等）
/*
SELECT 
    data_time,
    time,
    [申报出清], [实时计划], [预排计划], [超短期预测], [短期预测], 
    [开口小计划], [SCADA出清], [容量电价], [实时电价]
FROM (
    SELECT data_time, time, dict_label, energy
    FROM joined_data
) AS source_table
PIVOT (
    SUM(energy)
    FOR dict_label IN ([申报出清], [实时计划], [预排计划], [超短期预测], [短期预测], 
                      [开口小计划], [SCADA出清], [容量电价], [实时电价])
) AS pivot_table
ORDER BY data_time, time;
*/

-- 方案3: 动态列名版本（如果不确定具体的dict_label值）
/*
SELECT 
    data_time,
    time,
    SUM(CASE WHEN curve_type = 'sbcq_cq' THEN energy END) as sbcq_cq,
    SUM(CASE WHEN curve_type = 'ssjh_cq' THEN energy END) as ssjh_cq,
    SUM(CASE WHEN curve_type = 'yphjh_cq' THEN energy END) as yphjh_cq,
    SUM(CASE WHEN curve_type = 'cdqyc_cq' THEN energy END) as cdqyc_cq,
    SUM(CASE WHEN curve_type = 'dqyc_cq' THEN energy END) as dqyc_cq,
    SUM(CASE WHEN curve_type = 'kkxjh_cq' THEN energy END) as kkxjh_cq,
    SUM(CASE WHEN curve_type = 'scada_cq' THEN energy END) as scada_cq,
    SUM(CASE WHEN curve_type = 'rqdj_cq' THEN energy END) as rqdj_cq,
    SUM(CASE WHEN curve_type = 'ssdj_cq' THEN energy END) as ssdj_cq
FROM joined_data
GROUP BY data_time, time
ORDER BY data_time, time;
*/
