package com.bobandata.cloud.trade.flink.jdbc.input;

import com.bobandata.cloud.trade.flink.core.util.FlinkJdbcUtil;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.types.Row;

import java.io.IOException;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2024-03-29日 09:11
 * @description
 */
public class PlusJdbcRowInputFormat extends AbstractJdbcInputFormat<Row> {

    private RowTypeInfo rowTypeInfo;

    @Override
    public Row nextRecord(Row reuse) throws IOException {
        try {
            if (!hasNext) {
                return null;
            }
            for (int pos = 0; pos < reuse.getArity(); pos++) {
                reuse.setField(pos, this.getFieldValue(resultSet, pos + 1));
            }
            hasNext = resultSet.next();
            return reuse;
        } catch (SQLException se) {
            throw new IOException("Couldn't read data - " + se.getMessage(), se);
        } catch (NullPointerException npe) {
            throw new IOException("Couldn't access resultSet", npe);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    protected Object getFieldValue(ResultSet resultSet, int fieldPos) throws SQLException {
        TypeInformation<?> typeInformation = rowTypeInfo.getTypeAt(fieldPos);
        Integer jdbcType = FlinkJdbcUtil.getJdbcType(typeInformation);
        return this.getFieldValue(resultSet, jdbcType, fieldPos);
    }
}
