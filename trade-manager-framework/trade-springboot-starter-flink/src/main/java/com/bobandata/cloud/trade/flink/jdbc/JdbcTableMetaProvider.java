package com.bobandata.cloud.trade.flink.jdbc;

import com.bobandata.cloud.trade.flink.core.util.FlinkJdbcUtil;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.flink.table.types.logical.LogicalType;

/**
 * <AUTHOR>
 * @date 2024-03-21日 14:31
 * @description
 */
public class JdbcTableMetaProvider implements TableMetaProvider {

    @Override
    public TableMetaInfo loadTableMetaInfo(final String table, Connection connection) throws SQLException {
        TableMetaInfo.Builder builder = new TableMetaInfo.Builder()
                .setTableName(table);
        DatabaseMetaData metaData = connection.getMetaData();
        try (ResultSet resultSet = metaData.getColumns(null, null, table, null)) {
            while (resultSet.next()) {
                String columnName = resultSet.getString("COLUMN_NAME");
                int dataType = resultSet.getInt("DATA_TYPE");
                LogicalType logicalType = this.getLogicalType(dataType);
                builder.addField(columnName, dataType, logicalType);
                String dataTypeName = resultSet.getString("TYPE_NAME");
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return builder.build();
    }

    private LogicalType getLogicalType(int dataType) {
        return FlinkJdbcUtil.getLogicalType(dataType);
    }
}
