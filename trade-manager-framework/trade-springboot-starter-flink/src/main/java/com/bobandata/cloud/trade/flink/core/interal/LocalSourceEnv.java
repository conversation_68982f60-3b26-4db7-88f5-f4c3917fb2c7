package com.bobandata.cloud.trade.flink.core.interal;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.apache.flink.streaming.api.functions.source.SourceFunction;

/**
 * <AUTHOR>
 * @date 2024-09-18日 14:34
 * @description
 */
public class LocalSourceEnv {

    public <T> void doSourceFunc(SourceFunction<T> sourceFunction,
                                 ElementCollector<T> elementCollector,
                                 Configuration configuration) throws Exception {
        if (sourceFunction instanceof RichSourceFunction) {
            RichSourceFunction richSourceFunction = (RichSourceFunction) sourceFunction;
            richSourceFunction.open(configuration);
        }
        try {
            elementCollector.open(configuration);
            sourceFunction.run(new SingleSourceContext<>(elementCollector));
        } catch (Exception e) {
            throw e;
        } finally {
            sourceFunction.cancel();
            elementCollector.close();
        }

    }

    public <T> void doSourceFunc(SourceFunction<T> sourceFunction, SingleSourceContext<T> context) throws Exception {
        sourceFunction.run(context);
    }
}
