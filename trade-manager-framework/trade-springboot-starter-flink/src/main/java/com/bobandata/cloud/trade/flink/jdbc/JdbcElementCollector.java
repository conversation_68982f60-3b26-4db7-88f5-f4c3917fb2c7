package com.bobandata.cloud.trade.flink.jdbc;

import com.bobandata.cloud.trade.flink.core.interal.ElementCollector;
import java.sql.Connection;
import java.sql.SQLException;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.jdbc.internal.connection.JdbcConnectionProvider;
import org.apache.flink.connector.jdbc.internal.executor.JdbcBatchStatementExecutor;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @date 2024-10-21日 14:20
 * @description
 */
public class JdbcElementCollector implements ElementCollector<Row> {

    private final JdbcConnectionProvider jdbcConnectionProvider;

    private final JdbcBatchStatementExecutor<Row> statementExecutor;

    public JdbcElementCollector(JdbcConnectionProvider jdbcConnectionProvider, JdbcBatchStatementExecutor<Row> statementExecutor) {
        this.jdbcConnectionProvider = jdbcConnectionProvider;
        this.statementExecutor = statementExecutor;
    }

    @Override
    public void open(final Configuration configuration) throws SQLException, ClassNotFoundException {
        Connection connection = jdbcConnectionProvider.getOrEstablishConnection();
        statementExecutor.prepareStatements(connection);
    }

    @Override
    public void collect(final Row element) {

    }

    @Override
    public void close() {

    }
}
