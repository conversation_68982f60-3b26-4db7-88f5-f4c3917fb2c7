package com.bobandata.cloud.trade.flink.jdbc;

import java.sql.Connection;
import java.sql.SQLException;
import org.apache.flink.connector.jdbc.statement.FieldNamedPreparedStatement;
import org.apache.flink.connector.jdbc.statement.StatementFactory;

/**
 * <AUTHOR>
 * @date 2024-03-25日 09:25
 * @description 基于表信息生成最新的 #{@link FieldNamedPreparedStatement}
 *
 * <pre>
 *     Connection con = getConnection();
 *     String query = "select * from my_table where first_name=:name or last_name=:name";
 *     FieldNamedPreparedStatement st = FieldNamedPreparedStatement.prepareStatement(con, query, new String[]{"name"});
 *     st.setString(0, "bob");
 *     ResultSet rs = st.executeQuery();
 *  </pre>
 */
public class TableStatementFactoryImpl implements StatementFactory {

    private final String[] dataFieldNames;

    private final String execSQL;

    public TableStatementFactoryImpl(String[] dataFieldNames, String execSQL) {
        this.dataFieldNames = dataFieldNames;
        this.execSQL = execSQL;
    }

    @Override
    public FieldNamedPreparedStatement createStatement(final Connection connection) throws SQLException {
        return FieldNamedPreparedStatement.prepareStatement(connection, execSQL, dataFieldNames);
    }

}
