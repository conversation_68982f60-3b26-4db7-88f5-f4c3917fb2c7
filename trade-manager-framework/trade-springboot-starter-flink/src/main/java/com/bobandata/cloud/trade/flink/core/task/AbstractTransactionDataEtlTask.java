package com.bobandata.cloud.trade.flink.core.task;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * <AUTHOR>
 * @date 2024-07-19日 14:41
 * @description
 */
public abstract class AbstractTransactionDataEtlTask implements DataEtlTask {


    public void executeTransaction(Connection connection, String[] transactionSQLs) throws SQLException {
        connection.setAutoCommit(false);
        try (Statement statement = connection.createStatement()) {
            for (String transactionSQL : transactionSQLs) {
                statement.addBatch(transactionSQL);
            }
            statement.executeBatch();
            connection.commit();
        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException ex) {
                throw new RuntimeException(ex);
            }
            throw new RuntimeException(e);
        }
    }

    public void executeOrderSql(Connection connection, String prefixSQL, String[] transactionSQLs, String suffixSQL) throws SQLException {
        try (Statement statement = connection.createStatement()) {
            statement.execute(prefixSQL);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        this.executeTransaction(connection, transactionSQLs);
        try (Statement statement = connection.createStatement()) {
            statement.execute(suffixSQL);
            connection.commit();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }
}
