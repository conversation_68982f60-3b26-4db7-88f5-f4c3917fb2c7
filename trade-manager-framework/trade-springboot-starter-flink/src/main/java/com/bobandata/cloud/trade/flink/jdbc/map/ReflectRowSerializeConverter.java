package com.bobandata.cloud.trade.flink.jdbc.map;

import java.io.Serializable;
import java.lang.reflect.Field;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @date 2024-04-01日 11:19
 * @description
 */
public class ReflectRowSerializeConverter<T extends Serializable> implements RowSerializeConverter<T> {

    @Override
    public Row serialize(final T data) throws Exception {
        Class<? extends Serializable> clazz = data.getClass();
        Field[] fields = FieldUtils.getAllFields(clazz);
        Row row = new Row(fields.length);
        for (int i = 0; i < fields.length; i++) {
            Object value = FieldUtils.readField(fields[i], data, true);
            row.setField(i, value);
        }
        return row;
    }
}
