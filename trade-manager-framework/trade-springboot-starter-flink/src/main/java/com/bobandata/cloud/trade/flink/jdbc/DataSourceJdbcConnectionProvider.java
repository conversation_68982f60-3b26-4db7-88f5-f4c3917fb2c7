package com.bobandata.cloud.trade.flink.jdbc;

import com.bobandata.cloud.web.util.ApplicationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.connector.jdbc.internal.connection.JdbcConnectionProvider;

import javax.annotation.Nullable;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2024-03-21日 09:19
 * @description
 */
@Slf4j
public class DataSourceJdbcConnectionProvider implements JdbcConnectionProvider {

    private final transient DataSource dataSource;

    private transient Connection connection;

    public DataSourceJdbcConnectionProvider() {
        this.dataSource = ApplicationContextHolder.getBean(DataSource.class);
    }

    public DataSourceJdbcConnectionProvider(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    @Nullable
    @Override
    public Connection getConnection() {
        try {
            return this.getOrEstablishConnection();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean isConnectionValid() throws SQLException {
        return this.connection == null;
    }

    @Override
    public Connection getOrEstablishConnection() throws SQLException {
        if (this.connection == null) {
            this.connection = dataSource.getConnection();
        }
        return this.connection;
    }

    @Override
    public void closeConnection() {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                log.warn("JDBC connection close failed.", e);
            } finally {
                connection = null;
            }
        }
    }

    @Override
    public Connection reestablishConnection() throws SQLException, ClassNotFoundException {
        closeConnection();
        return getOrEstablishConnection();
    }
}
