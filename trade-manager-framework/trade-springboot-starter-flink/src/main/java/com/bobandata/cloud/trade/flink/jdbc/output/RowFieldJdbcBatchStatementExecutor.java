package com.bobandata.cloud.trade.flink.jdbc.output;

import com.bobandata.cloud.trade.flink.annotation.RowField;
import com.bobandata.cloud.trade.flink.core.util.FlinkJdbcUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.connector.jdbc.dialect.mysql.MySqlDialect;
import org.apache.flink.connector.jdbc.internal.executor.JdbcBatchStatementExecutor;
import org.apache.flink.connector.jdbc.utils.JdbcUtils;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-07-16日 10:59
 * @description
 */
public class RowFieldJdbcBatchStatementExecutor<T> implements JdbcBatchStatementExecutor<T>, Serializable {

    private final String tableName;

    private final Class<T> clazz;

    private transient PreparedStatement insertPreparedStatement;

    public RowFieldJdbcBatchStatementExecutor(String tableName, Class<T> clazz) {
        this.tableName = tableName;
        this.clazz = clazz;
    }

    private String generateExecuteInsertSql() {
        Field[] fields = this.clazz.getDeclaredFields();
        List<String> colNames = new ArrayList<>();
        for (Field currentField : fields) {
            RowField rowField = currentField.getAnnotation(RowField.class);
            if (rowField == null) {
                continue;
            }
            currentField.setAccessible(true);
            String colName = rowField.colName();
            colName = StringUtils.isEmpty(colName) ? currentField.getName() : colName;
            colNames.add(colName);
        }
        return FlinkJdbcUtil.getInsertSql(this.tableName, new MySqlDialect(), colNames.toArray(new String[0]));
    }

    @Override
    public void prepareStatements(Connection connection) throws SQLException {
        String insertSql = this.generateExecuteInsertSql();
        this.insertPreparedStatement = connection.prepareStatement(insertSql);
    }

    @Override
    public void addToBatch(T record) throws SQLException {
        int index = 0;
        for (Field currentField : clazz.getDeclaredFields()) {
            currentField.setAccessible(true);
            RowField rowField = currentField.getAnnotation(RowField.class);
            if (rowField == null) {
                continue;
            }
            try {
                Object value = currentField.get(record);
                int jdbcType = rowField.jdbcType();
                JdbcUtils.setField(insertPreparedStatement, jdbcType, value, index);
                index++;
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
        insertPreparedStatement.addBatch();
    }

    @Override
    public void executeBatch() throws SQLException {
        insertPreparedStatement.executeBatch();
    }

    @Override
    public void closeStatements() throws SQLException {
        insertPreparedStatement.executeBatch();
        insertPreparedStatement.close();
    }
}
