package com.bobandata.cloud.trade.flink.config;

import com.bobandata.cloud.trade.flink.jdbc.DataSourceJdbcConnectionProvider;
import org.apache.flink.connector.jdbc.internal.connection.JdbcConnectionProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2024-03-29日 10:10
 * @description
 */
@AutoConfiguration
@EnableConfigurationProperties(TradeFlinkProperties.class)
public class TradeFlinkAutoConfiguration {

    @Bean
    public JdbcConnectionProvider getJdbcConnectionProvider(DataSource dataSource){
        return new DataSourceJdbcConnectionProvider(dataSource);
    }
}
