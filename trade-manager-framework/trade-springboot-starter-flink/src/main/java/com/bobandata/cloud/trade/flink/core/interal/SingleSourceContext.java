package com.bobandata.cloud.trade.flink.core.interal;

import java.util.function.Consumer;
import lombok.Setter;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.streaming.api.watermark.Watermark;

/**
 * <AUTHOR>
 * @date 2024-09-18日 14:53
 * @description 单一的发送记录上下文 需要记录发送数据统计信息
 */
public class SingleSourceContext<T> implements SourceFunction.SourceContext<T> {

    private final ElementCollector<T> elementCollector;

    @Setter
    private Consumer<T> elementConsumer;

    public SingleSourceContext(ElementCollector<T> elementCollector) {
        this.elementCollector = elementCollector;
    }

    @Override
    public void collect(final T element) {
        elementCollector.collect(element);
        if (elementConsumer != null) {
            elementConsumer.accept(element);
        }
    }

    @Override
    public void collectWithTimestamp(final T element, final long timestamp) {
        collect(element);
    }

    @Override
    public void emitWatermark(final Watermark mark) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void markAsTemporarilyIdle() {
        throw new UnsupportedOperationException();
    }

    @Override
    public Object getCheckpointLock() {
        throw new UnsupportedOperationException();
    }

    @Override
    public void close() {
        elementCollector.close();
    }
}
