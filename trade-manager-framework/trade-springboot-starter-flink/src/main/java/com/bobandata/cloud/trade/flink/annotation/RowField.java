package com.bobandata.cloud.trade.flink.annotation;

import java.lang.annotation.*;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.ANNOTATION_TYPE})
public @interface RowField {

    /**
     *
     * @return {@link java.sql.Types}
     */
    int jdbcType();

    /**
     *
     * @return 数据库字段名称
     */
    String colName() default "";

    boolean exist() default true;
}
