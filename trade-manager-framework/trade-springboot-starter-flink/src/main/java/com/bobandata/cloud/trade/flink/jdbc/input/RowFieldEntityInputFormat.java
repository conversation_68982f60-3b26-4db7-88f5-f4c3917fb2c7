package com.bobandata.cloud.trade.flink.jdbc.input;

import com.bobandata.cloud.trade.flink.annotation.RowField;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.GenericTypeInfo;
import org.apache.flink.api.java.typeutils.ResultTypeQueryable;
import org.apache.flink.connector.jdbc.internal.connection.JdbcConnectionProvider;
import org.apache.flink.connector.jdbc.split.JdbcParameterValuesProvider;
import org.apache.flink.util.Preconditions;

import java.io.IOException;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2024-07-12日 15:44
 * @description
 */
public class RowFieldEntityInputFormat<T extends Serializable> extends AbstractJdbcInputFormat<T> implements ResultTypeQueryable<T> {

    private Class<T> clazz;

    @Override
    public T nextRecord(T reuse) throws IOException {
        Field[] fields = clazz.getDeclaredFields();
        T data;
        try {
            data = clazz.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        try {
            if (!hasNext) {
                return null;
            }
            for (Field currentField : fields) {
                currentField.setAccessible(true);
                RowField rowField = currentField.getAnnotation(RowField.class);
                if (rowField == null) {
                    continue;
                }
                String colName = rowField.colName();
                colName = StringUtils.isEmpty(colName) ? currentField.getName() : colName;
                Object fieldValue = this.getFieldValue(resultSet, rowField.jdbcType(), colName);
                currentField.set(data, fieldValue);
            }
            hasNext = resultSet.next();
            return data;
        } catch (SQLException se) {
            throw new IOException("Couldn't read data - " + se.getMessage(), se);
        } catch (NullPointerException npe) {
            throw new IOException("Couldn't access resultSet", npe);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static <T extends Serializable> Builder<T> buildJdbcInputFormat() {
        return new Builder<T>();
    }

    @Override
    public TypeInformation<T> getProducedType() {
        return new GenericTypeInfo<>(clazz);
    }

    /**
     * Builder for {@link RowFieldEntityInputFormat}.
     */
    public static class Builder<T extends Serializable> {
        private final RowFieldEntityInputFormat<T> format;

        public Builder() {
            this.format = new RowFieldEntityInputFormat<>();
            this.format.resultSetType = ResultSet.TYPE_FORWARD_ONLY;
            this.format.resultSetConcurrency = ResultSet.CONCUR_READ_ONLY;
        }

        public Builder<T> setClazz(Class<T> clazz) {
            this.format.clazz = clazz;
            return this;
        }

        public Builder<T> setQuery(String query) {
            format.queryTemplate = query;
            return this;
        }

        public Builder<T> setResultSetType(int resultSetType) {
            format.resultSetType = resultSetType;
            return this;
        }

        public Builder<T> setResultSetConcurrency(int resultSetConcurrency) {
            format.resultSetConcurrency = resultSetConcurrency;
            return this;
        }

        public Builder<T> setParametersProvider(
                JdbcParameterValuesProvider parameterValuesProvider) {
            format.parameterValues = parameterValuesProvider.getParameterValues();
            return this;
        }


        public Builder<T> setFetchSize(int fetchSize) {
            Preconditions.checkArgument(
                    fetchSize == Integer.MIN_VALUE || fetchSize > 0,
                    "Illegal value %s for fetchSize, has to be positive or Integer.MIN_VALUE.",
                    fetchSize);
            format.fetchSize = fetchSize;
            return this;
        }

        public Builder<T> setAutoCommit(Boolean autoCommit) {
            format.autoCommit = autoCommit;
            return this;
        }

        public Builder<T> setJdbcConnectionProvider(JdbcConnectionProvider jdbcConnectionProvider) {
            this.format.connectionProvider = jdbcConnectionProvider;
            return this;
        }

        public RowFieldEntityInputFormat<T> finish() {
            if (format.queryTemplate == null) {
                throw new NullPointerException("No query supplied");
            }

            if (format.parameterValues == null) {
                LOG.debug("No input splitting configured (data will be read with parallelism 1).");
            }
            return format;
        }
    }
}
