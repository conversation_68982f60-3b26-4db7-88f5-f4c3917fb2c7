<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bobandata.cloud</groupId>
        <artifactId>trade-manager-framework</artifactId>
        <version>${re.version}</version>
    </parent>

    <artifactId>trade-springboot-starter-flink</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-framework-common</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-springboot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-springboot-starter-curve</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-springboot-starter-mybatisplus</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-api-java-bridge</artifactId>
        </dependency>


        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-clients</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-connector-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-common</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>