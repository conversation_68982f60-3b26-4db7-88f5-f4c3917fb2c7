server:
  port: 20083
  servlet:
    context-path: /trade/elec
spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  datasource:
    url: jdbc:mysql://${DATABASE_HOST:*************}:${DATABASE_PORT:31987}/${DATABASE_DB:trade_data_warehouse}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=GMT%2B8
    username: ${DATABASE_USER:root}
    password: ${DATABASE_PASSWORD:root}
    hikari:
      maximumPoolSize: 10
      minimumIdle: 2
      idleTimeout: 600000
      connectionTimeout: 30000
      maxLifetime: 1800000
      driver-class-name: com.mysql.jdbc.Driver
      connection-test-query: select 1
    dynamic:
      hikari:
        maximumPoolSize: 10
        minimumIdle: 2
        idleTimeout: 600000
        connectionTimeout: 30000
        maxLifetime: 1800000
        connection-test-query: select 1
      datasource:
        master:
          url: jdbc:mysql://${DATABASE_HOST:*************}:${DATABASE_PORT:30488}/${DATABASE_DB:spot_sync}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=GMT%2B8
          driver-class-name: com.mysql.jdbc.Driver
          username: ${DATABASE_USER:root}
          password: ${DATABASE_PASSWORD:root}
        source_db:
          url: **********************************************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          username: root
          password: root
        middle_db:
          url: jdbc:mysql://*************:30488/middle_db?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=GMT%2B8
          driver-class-name: com.mysql.jdbc.Driver
          username: root
          password: root
        target_db:
          url: jdbc:mysql://*************:30488/target_db?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=GMT%2B8
          driver-class-name: com.mysql.jdbc.Driver
          username: root
          password: root
        source_history:
          url: ************************************************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          username: root
          password: root
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 30MB
  devtools:
    restart:
      enabled: true

logging:
  config: classpath:logback.xml

mybatis-plus:
  mapper-locations:
    - classpath*:mapper/*.xml
  typeAliasesPackage: com.bobandata.cloud.**.dataobject
  global-config:
    db-config:
      id-type: auto
      logic-delete-value: 1
      logic-not-delete-value: 0
    enable-sql-runner: true
  #    sql-injector: com.baomidou.mybatisplus.mapper.LogicSqlInjector
  #    meta-object-handler: com.bobandata.acquarium.core.server.handler.BaseEntityHandler
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    jdbc-type-for-null: 'null'
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
#    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

springdoc:
  api-docs:
    enabled: true # 1. 是否开启 Swagger 接文档的元数据
    path: /v3/api-docs
  swagger-ui:
    enabled: true # 2.1 是否开启 Swagger 文档的官方 UI 界面
    path: /swagger-ui.html

knife4j:
  enable: true # 2.2 是否开启 Swagger 文档的 Knife4j UI 界面
  setting:
    language: zh_cn


ribbon:
  ReadTimeout: 120000 # 请求处理的超时时间

trade:
  idGen:
    type: sys
  file-config:
    default-path-prefix: /upload/
    default-config-id: 1
    limitFileType: pdf
    client-configs:
      - config-id: 1
        file-storage-enum: s3
        properties:
          endpoint: http://*************:9000
          domain: http://*************:9000
          bucket: project-manager-develop
          accessKey: Rtrv86RXJxDyMLj5
          accessSecret: 80nVcXH9mIukmmemIZbCKpUeVajB9iHZ
      - config-id: 2
        file-storage-enum: s3
        properties:
          endpoint: http://*************:9000
          domain: http://*************:9000
          bucket: spot-data-sync
          accessKey: Rtrv86RXJxDyMLj5
          accessSecret: 80nVcXH9mIukmmemIZbCKpUeVajB9iHZ
      - config-id: 3
        file-storage-enum: s3
        properties:
          endpoint: http://*************:9000
          domain: http://*************:9000
          bucket: history
          accessKey: Rtrv86RXJxDyMLj5
          accessSecret: 80nVcXH9mIukmmemIZbCKpUeVajB9iHZ
  data-permission:
    global-enable: true
    ignore-tables:
    org-rule:
      org-tables:
        - data_items_def_info
        - data_pub_trace
    type: orgNo
  security:
    permit-all-urls:
      - /trade/cloud/doc.html
    mock-enable: true
    pre-authenticated-enable: false
  manager:
    system:
      server: localhost:20081
  process:
    enable: false
  sync:
    source-file-config-id: 1
    target-file-config-id: 2
    sync-day-cron: 0 0 1 1/1 * ? #每天一点执行一次
    sync-week-cron: 0 0 1 ? * FRI #每周五一点执行一次
    sync-month-cron: 0 25 09 22 1/1 ? #每月25号8点执行一次
    sync-year-cron: 0 0 08 25 12 ? #每年12月25号8点执行一次
