server:
  port: 20083
  servlet:
    context-path: /trade/elec
spring:
  application:
    name: trade-manager-system
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  datasource:
    url: jdbc:mysql://${DATABASE_HOST:*************}:${DATABASE_PORT:30488}/${DATABASE_DB:spot_sync}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=GMT%2B8
    username: ${DATABASE_USER:root}
    password: ${DATABASE_PASSWORD:root}
    hikari:
      maximumPoolSize: 10
      minimumIdle: 2
      idleTimeout: 600000
      connectionTimeout: 30000
      maxLifetime: 1800000
      driver-class-name: com.mysql.jdbc.Driver
      connection-test-query: select 1
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 30MB

logging:
  config: classpath:logback.xml

mybatis-plus:
  mapper-locations:
    - classpath*:mapper/*.xml
  typeAliasesPackage: com.bobandata.cloud.**.dataobject
  global-config:
    db-config:
      id-type: auto
      logic-delete-value: 1
      logic-not-delete-value: 0
  #    sql-injector: com.baomidou.mybatisplus.mapper.LogicSqlInjector
  #    meta-object-handler: com.bobandata.acquarium.core.server.handler.BaseEntityHandler
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    jdbc-type-for-null: 'null'
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
#    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

springdoc:
  api-docs:
    enabled: true # 1. 是否开启 Swagger 接文档的元数据
    path: /v3/api-docs
  swagger-ui:
    enabled: true # 2.1 是否开启 Swagger 文档的官方 UI 界面
    path: /swagger-ui.html

knife4j:
  enable: true # 2.2 是否开启 Swagger 文档的 Knife4j UI 界面
  setting:
    language: zh_cn


ribbon:
  ReadTimeout: 120000 # 请求处理的超时时间

trade:
  security:
    permit-all-urls:
      - /trade/system/doc.html
    mock-enable: true
    pre-authenticated-enable: false
  file-config:
    default-path-prefix: /upload/
    default-config-id: 1
    limitFileType: pdf
    client-configs:
      - config-id: 1
        file-storage-enum: s3
        properties:
          endpoint: http://192.168.2.103:9000
          domain: http://192.168.2.103:9000
          bucket: project-manager-develop
          accessKey: Rtrv86RXJxDyMLj5
          accessSecret: 80nVcXH9mIukmmemIZbCKpUeVajB9iHZ
