<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bobandata.cloud.trade.elec.dal.mysql.mlTerm.MlTradeLinkSpotCurveMapper">

    <select id="list"
            resultType="java.util.Map">
        SELECT
        ps.curve_date,
        ps.curve_name,
        ps.dict_label as curve_type,
        SUM(ps.point_sum) AS calculated_value
        FROM (
        -- 子查询替代 PointSums CTE
        SELECT
        a.curve_name,
        a.curve_date,
        c.dict_label,
        (
        COALESCE(p1, 0) + COALESCE(p2, 0) + COALESCE(p3, 0) + COALESCE(p4, 0) + COALESCE(p5, 0) +
        COALESCE(p6, 0) + COALESCE(p7, 0) + COALESCE(p8, 0) + COALESCE(p9, 0) + COALESCE(p10, 0) +
        COALESCE(p11, 0) + COALESCE(p12, 0) + COALESCE(p13, 0) + COALESCE(p14, 0) + COALESCE(p15, 0) +
        COALESCE(p16, 0) + COALESCE(p17, 0) + COALESCE(p18, 0) + COALESCE(p19, 0) + COALESCE(p20, 0) +
        COALESCE(p21, 0) + COALESCE(p22, 0) + COALESCE(p23, 0) + COALESCE(p24, 0) + COALESCE(p25, 0) +
        COALESCE(p26, 0) + COALESCE(p27, 0) + COALESCE(p28, 0) + COALESCE(p29, 0) + COALESCE(p30, 0) +
        COALESCE(p31, 0) + COALESCE(p32, 0) + COALESCE(p33, 0) + COALESCE(p34, 0) + COALESCE(p35, 0) +
        COALESCE(p36, 0) + COALESCE(p37, 0) + COALESCE(p38, 0) + COALESCE(p39, 0) + COALESCE(p40, 0) +
        COALESCE(p41, 0) + COALESCE(p42, 0) + COALESCE(p43, 0) + COALESCE(p44, 0) + COALESCE(p45, 0) +
        COALESCE(p46, 0) + COALESCE(p47, 0) + COALESCE(p48, 0) + COALESCE(p49, 0) + COALESCE(p50, 0) +
        COALESCE(p51, 0) + COALESCE(p52, 0) + COALESCE(p53, 0) + COALESCE(p54, 0) + COALESCE(p55, 0) +
        COALESCE(p56, 0) + COALESCE(p57, 0) + COALESCE(p58, 0) + COALESCE(p59, 0) + COALESCE(p60, 0) +
        COALESCE(p61, 0) + COALESCE(p62, 0) + COALESCE(p63, 0) + COALESCE(p64, 0) + COALESCE(p65, 0) +
        COALESCE(p66, 0) + COALESCE(p67, 0) + COALESCE(p68, 0) + COALESCE(p69, 0) + COALESCE(p70, 0) +
        COALESCE(p71, 0) + COALESCE(p72, 0) + COALESCE(p73, 0) + COALESCE(p74, 0) + COALESCE(p75, 0) +
        COALESCE(p76, 0) + COALESCE(p77, 0) + COALESCE(p78, 0) + COALESCE(p79, 0) + COALESCE(p80, 0) +
        COALESCE(p81, 0) + COALESCE(p82, 0) + COALESCE(p83, 0) + COALESCE(p84, 0) + COALESCE(p85, 0) +
        COALESCE(p86, 0) + COALESCE(p87, 0) + COALESCE(p88, 0) + COALESCE(p89, 0) + COALESCE(p90, 0) +
        COALESCE(p91, 0) + COALESCE(p92, 0) + COALESCE(p93, 0) + COALESCE(p94, 0) + COALESCE(p95, 0) +
        COALESCE(p96, 0)
        ) AS point_sum
        FROM `dwd_ml_trade_link_spot_curve` a
        left join sys_dict_code c on a.curve_type = c.dict_code
        WHERE a.curve_type IN ('DL', 'DF') and curve_date between #{startTime} and #{endTime} and trade_unit_code = #{unitCode}
        and c.dict_type = 'zcq'
        ) AS ps
        GROUP BY ps.curve_date, ps.curve_name, ps.dict_label

        UNION ALL

        -- 第二部分：计算 DJ 类型的加权平均值
        SELECT
        djc.curve_date,
        djc.curve_name,
        '电价' AS curve_type,
        SUM(djc.weighted_product_sum) / NULLIF(SUM(djc.total_dl), 0) AS calculated_value
        FROM (
        -- 子查询替代 DJ_Calculations CTE，内含 DJ_Info 和 DL_Info 的逻辑
        SELECT
        dj.curve_date,
        dj.curve_name,
        (dj.dj_p1 * dl.dl_p1 + dj.dj_p2 * dl.dl_p2 + dj.dj_p3 * dl.dl_p3 + dj.dj_p4 * dl.dl_p4 + dj.dj_p5 * dl.dl_p5 + dj.dj_p6 * dl.dl_p6 + dj.dj_p7 * dl.dl_p7 + dj.dj_p8 * dl.dl_p8 + dj.dj_p9 * dl.dl_p9 + dj.dj_p10 * dl.dl_p10 + dj.dj_p11 * dl.dl_p11 + dj.dj_p12 * dl.dl_p12 + dj.dj_p13 * dl.dl_p13 + dj.dj_p14 * dl.dl_p14 + dj.dj_p15 * dl.dl_p15 + dj.dj_p16 * dl.dl_p16 + dj.dj_p17 * dl.dl_p17 + dj.dj_p18 * dl.dl_p18 + dj.dj_p19 * dl.dl_p19 + dj.dj_p20 * dl.dl_p20 + dj.dj_p21 * dl.dl_p21 + dj.dj_p22 * dl.dl_p22 + dj.dj_p23 * dl.dl_p23 + dj.dj_p24 * dl.dl_p24 + dj.dj_p25 * dl.dl_p25 + dj.dj_p26 * dl.dl_p26 + dj.dj_p27 * dl.dl_p27 + dj.dj_p28 * dl.dl_p28 + dj.dj_p29 * dl.dl_p29 + dj.dj_p30 * dl.dl_p30 + dj.dj_p31 * dl.dl_p31 + dj.dj_p32 * dl.dl_p32 + dj.dj_p33 * dl.dl_p33 + dj.dj_p34 * dl.dl_p34 + dj.dj_p35 * dl.dl_p35 + dj.dj_p36 * dl.dl_p36 + dj.dj_p37 * dl.dl_p37 + dj.dj_p38 * dl.dl_p38 + dj.dj_p39 * dl.dl_p39 + dj.dj_p40 * dl.dl_p40 + dj.dj_p41 * dl.dl_p41 + dj.dj_p42 * dl.dl_p42 + dj.dj_p43 * dl.dl_p43 + dj.dj_p44 * dl.dl_p44 + dj.dj_p45 * dl.dl_p45 + dj.dj_p46 * dl.dl_p46 + dj.dj_p47 * dl.dl_p47 + dj.dj_p48 * dl.dl_p48 + dj.dj_p49 * dl.dl_p49 + dj.dj_p50 * dl.dl_p50 + dj.dj_p51 * dl.dl_p51 + dj.dj_p52 * dl.dl_p52 + dj.dj_p53 * dl.dl_p53 + dj.dj_p54 * dl.dl_p54 + dj.dj_p55 * dl.dl_p55 + dj.dj_p56 * dl.dl_p56 + dj.dj_p57 * dl.dl_p57 + dj.dj_p58 * dl.dl_p58 + dj.dj_p59 * dl.dl_p59 + dj.dj_p60 * dl.dl_p60 + dj.dj_p61 * dl.dl_p61 + dj.dj_p62 * dl.dl_p62 + dj.dj_p63 * dl.dl_p63 + dj.dj_p64 * dl.dl_p64 + dj.dj_p65 * dl.dl_p65 + dj.dj_p66 * dl.dl_p66 + dj.dj_p67 * dl.dl_p67 + dj.dj_p68 * dl.dl_p68 + dj.dj_p69 * dl.dl_p69 + dj.dj_p70 * dl.dl_p70 + dj.dj_p71 * dl.dl_p71 + dj.dj_p72 * dl.dl_p72 + dj.dj_p73 * dl.dl_p73 + dj.dj_p74 * dl.dl_p74 + dj.dj_p75 * dl.dl_p75 + dj.dj_p76 * dl.dl_p76 + dj.dj_p77 * dl.dl_p77 + dj.dj_p78 * dl.dl_p78 + dj.dj_p79 * dl.dl_p79 + dj.dj_p80 * dl.dl_p80 + dj.dj_p81 * dl.dl_p81 + dj.dj_p82 * dl.dl_p82 + dj.dj_p83 * dl.dl_p83 + dj.dj_p84 * dl.dl_p84 + dj.dj_p85 * dl.dl_p85 + dj.dj_p86 * dl.dl_p86 + dj.dj_p87 * dl.dl_p87 + dj.dj_p88 * dl.dl_p88 + dj.dj_p89 * dl.dl_p89 + dj.dj_p90 * dl.dl_p90 + dj.dj_p91 * dl.dl_p91 + dj.dj_p92 * dl.dl_p92 + dj.dj_p93 * dl.dl_p93 + dj.dj_p94 * dl.dl_p94 + dj.dj_p95 * dl.dl_p95 + dj.dj_p96 * dl.dl_p96) AS weighted_product_sum,
        dl.total_dl
        FROM (
        -- 子查询替代 DJ_Info CTE
        SELECT
        a.curve_name,
        a.curve_date,
        a.p1 AS dj_p1, a.p2 AS dj_p2, a.p3 AS dj_p3, a.p4 AS dj_p4, a.p5 AS dj_p5, a.p6 AS dj_p6, a.p7 AS dj_p7, a.p8 AS dj_p8, a.p9 AS dj_p9, a.p10 AS dj_p10, a.p11 AS dj_p11, a.p12 AS dj_p12, a.p13 AS dj_p13, a.p14 AS dj_p14, a.p15 AS dj_p15, a.p16 AS dj_p16, a.p17 AS dj_p17, a.p18 AS dj_p18, a.p19 AS dj_p19, a.p20 AS dj_p20, a.p21 AS dj_p21, a.p22 AS dj_p22, a.p23 AS dj_p23, a.p24 AS dj_p24, a.p25 AS dj_p25, a.p26 AS dj_p26, a.p27 AS dj_p27, a.p28 AS dj_p28, a.p29 AS dj_p29, a.p30 AS dj_p30, a.p31 AS dj_p31, a.p32 AS dj_p32, a.p33 AS dj_p33, a.p34 AS dj_p34, a.p35 AS dj_p35, a.p36 AS dj_p36, a.p37 AS dj_p37, a.p38 AS dj_p38, a.p39 AS dj_p39, a.p40 AS dj_p40, a.p41 AS dj_p41, a.p42 AS dj_p42, a.p43 AS dj_p43, a.p44 AS dj_p44, a.p45 AS dj_p45, a.p46 AS dj_p46, a.p47 AS dj_p47, a.p48 AS dj_p48, a.p49 AS dj_p49, a.p50 AS dj_p50, a.p51 AS dj_p51, a.p52 AS dj_p52, a.p53 AS dj_p53, a.p54 AS dj_p54, a.p55 AS dj_p55, a.p56 AS dj_p56, a.p57 AS dj_p57, a.p58 AS dj_p58, a.p59 AS dj_p59, a.p60 AS dj_p60, a.p61 AS dj_p61, a.p62 AS dj_p62, a.p63 AS dj_p63, a.p64 AS dj_p64, a.p65 AS dj_p65, a.p66 AS dj_p66, a.p67 AS dj_p67, a.p68 AS dj_p68, a.p69 AS dj_p69, a.p70 AS dj_p70, a.p71 AS dj_p71, a.p72 AS dj_p72, a.p73 AS dj_p73, a.p74 AS dj_p74, a.p75 AS dj_p75, a.p76 AS dj_p76, a.p77 AS dj_p77, a.p78 AS dj_p78, a.p79 AS dj_p79, a.p80 AS dj_p80, a.p81 AS dj_p81, a.p82 AS dj_p82, a.p83 AS dj_p83, a.p84 AS dj_p84, a.p85 AS dj_p85, a.p86 AS dj_p86, a.p87 AS dj_p87, a.p88 AS dj_p88, a.p89 AS dj_p89, a.p90 AS dj_p90, a.p91 AS dj_p91, a.p92 AS dj_p92, a.p93 AS dj_p93, a.p94 AS dj_p94, a.p95 AS dj_p95, a.p96 AS dj_p96
        FROM `dwd_ml_trade_link_spot_curve` a
        WHERE a.curve_type = 'DJ' and curve_date between #{startTime} and #{endTime} and trade_unit_code = #{unitCode}
        ) AS dj
        JOIN (
        -- 子查询替代 DL_Info CTE
        SELECT
        curve_name,
        curve_date,
        (p1 + p2 + p3 + p4 + p5 + p6 + p7 + p8 + p9 + p10 + p11 + p12 + p13 + p14 + p15 + p16 + p17 + p18 + p19 + p20 + p21 + p22 + p23 + p24 + p25 + p26 + p27 + p28 + p29 + p30 + p31 + p32 + p33 + p34 + p35 + p36 + p37 + p38 + p39 + p40 + p41 + p42 + p43 + p44 + p45 + p46 + p47 + p48 + p49 + p50 + p51 + p52 + p53 + p54 + p55 + p56 + p57 + p58 + p59 + p60 + p61 + p62 + p63 + p64 + p65 + p66 + p67 + p68 + p69 + p70 + p71 + p72 + p73 + p74 + p75 + p76 + p77 + p78 + p79 + p80 + p81 + p82 + p83 + p84 + p85 + p86 + p87 + p88 + p89 + p90 + p91 + p92 + p93 + p94 + p95 + p96) AS total_dl,
        p1 AS dl_p1, p2 AS dl_p2, p3 AS dl_p3, p4 AS dl_p4, p5 AS dl_p5, p6 AS dl_p6, p7 AS dl_p7, p8 AS dl_p8, p9 AS dl_p9, p10 AS dl_p10, p11 AS dl_p11, p12 AS dl_p12, p13 AS dl_p13, p14 AS dl_p14, p15 AS dl_p15, p16 AS dl_p16, p17 AS dl_p17, p18 AS dl_p18, p19 AS dl_p19, p20 AS dl_p20, p21 AS dl_p21, p22 AS dl_p22, p23 AS dl_p23, p24 AS dl_p24, p25 AS dl_p25, p26 AS dl_p26, p27 AS dl_p27, p28 AS dl_p28, p29 AS dl_p29, p30 AS dl_p30, p31 AS dl_p31, p32 AS dl_p32, p33 AS dl_p33, p34 AS dl_p34, p35 AS dl_p35, p36 AS dl_p36, p37 AS dl_p37, p38 AS dl_p38, p39 AS dl_p39, p40 AS dl_p40, p41 AS dl_p41, p42 AS dl_p42, p43 AS dl_p43, p44 AS dl_p44, p45 AS dl_p45, p46 AS dl_p46, p47 AS dl_p47, p48 AS dl_p48, p49 AS dl_p49, p50 AS dl_p50, p51 AS dl_p51, p52 AS dl_p52, p53 AS dl_p53, p54 AS dl_p54, p55 AS dl_p55, p56 AS dl_p56, p57 AS dl_p57, p58 AS dl_p58, p59 AS dl_p59, p60 AS dl_p60, p61 AS dl_p61, p62 AS dl_p62, p63 AS dl_p63, p64 AS dl_p64, p65 AS dl_p65, p66 AS dl_p66, p67 AS dl_p67, p68 AS dl_p68, p69 AS dl_p69, p70 AS dl_p70, p71 AS dl_p71, p72 AS dl_p72, p73 AS dl_p73, p74 AS dl_p74, p75 AS dl_p75, p76 AS dl_p76, p77 AS dl_p77, p78 AS dl_p78, p79 AS dl_p79, p80 AS dl_p80, p81 AS dl_p81, p82 AS dl_p82, p83 AS dl_p83, p84 AS dl_p84, p85 AS dl_p85, p86 AS dl_p86, p87 AS dl_p87, p88 AS dl_p88, p89 AS dl_p89, p90 AS dl_p90, p91 AS dl_p91, p92 AS dl_p92, p93 AS dl_p93, p94 AS dl_p94, p95 AS dl_p95, p96 AS dl_p96
        FROM `dwd_ml_trade_link_spot_curve`
        WHERE curve_type = 'DL' and curve_date between #{startTime} and #{endTime} and trade_unit_code = #{unitCode}
        ) AS dl ON dj.curve_name = dl.curve_name AND dj.curve_date = dl.curve_date
        ) AS djc
        WHERE djc.total_dl IS NOT NULL AND djc.total_dl != 0 -- 排除没有匹配 DL 行或 DL 总和为 0 的情况
        GROUP BY djc.curve_date, djc.curve_name

        ORDER BY curve_date, curve_name, curve_type;
    </select>
    <select id="getCurve" resultType="com.bobandata.cloud.trade.elec.controller.mlTerm.vo.MlTradeHoldCurveListRespVo">
        SELECT
        CONCAT(a.curve_name,c.dict_label) curveType,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p1) ELSE SUM(a.p1) END p1,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p2) ELSE SUM(a.p2) END p2,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p3) ELSE SUM(a.p3) END p3,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p4) ELSE SUM(a.p4) END p4,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p5) ELSE SUM(a.p5) END p5,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p6) ELSE SUM(a.p6) END p6,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p7) ELSE SUM(a.p7) END p7,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p8) ELSE SUM(a.p8) END p8,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p9) ELSE SUM(a.p9) END p9,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p10) ELSE SUM(a.p10) END p10,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p11) ELSE SUM(a.p11) END p11,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p12) ELSE SUM(a.p12) END p12,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p13) ELSE SUM(a.p13) END p13,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p14) ELSE SUM(a.p14) END p14,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p15) ELSE SUM(a.p15) END p15,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p16) ELSE SUM(a.p16) END p16,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p17) ELSE SUM(a.p17) END p17,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p18) ELSE SUM(a.p18) END p18,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p19) ELSE SUM(a.p19) END p19,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p20) ELSE SUM(a.p20) END p20,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p21) ELSE SUM(a.p21) END p21,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p22) ELSE SUM(a.p22) END p22,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p23) ELSE SUM(a.p23) END p23,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p24) ELSE SUM(a.p24) END p24,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p25) ELSE SUM(a.p25) END p25,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p26) ELSE SUM(a.p26) END p26,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p27) ELSE SUM(a.p27) END p27,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p28) ELSE SUM(a.p28) END p28,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p29) ELSE SUM(a.p29) END p29,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p30) ELSE SUM(a.p30) END p30,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p31) ELSE SUM(a.p31) END p31,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p32) ELSE SUM(a.p32) END p32,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p33) ELSE SUM(a.p33) END p33,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p34) ELSE SUM(a.p34) END p34,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p35) ELSE SUM(a.p35) END p35,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p36) ELSE SUM(a.p36) END p36,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p37) ELSE SUM(a.p37) END p37,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p38) ELSE SUM(a.p38) END p38,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p39) ELSE SUM(a.p39) END p39,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p40) ELSE SUM(a.p40) END p40,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p41) ELSE SUM(a.p41) END p41,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p42) ELSE SUM(a.p42) END p42,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p43) ELSE SUM(a.p43) END p43,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p44) ELSE SUM(a.p44) END p44,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p45) ELSE SUM(a.p45) END p45,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p46) ELSE SUM(a.p46) END p46,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p47) ELSE SUM(a.p47) END p47,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p48) ELSE SUM(a.p48) END p48,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p49) ELSE SUM(a.p49) END p49,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p50) ELSE SUM(a.p50) END p50,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p51) ELSE SUM(a.p51) END p51,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p52) ELSE SUM(a.p52) END p52,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p53) ELSE SUM(a.p53) END p53,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p54) ELSE SUM(a.p54) END p54,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p55) ELSE SUM(a.p55) END p55,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p56) ELSE SUM(a.p56) END p56,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p57) ELSE SUM(a.p57) END p57,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p58) ELSE SUM(a.p58) END p58,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p59) ELSE SUM(a.p59) END p59,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p60) ELSE SUM(a.p60) END p60,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p61) ELSE SUM(a.p61) END p61,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p62) ELSE SUM(a.p62) END p62,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p63) ELSE SUM(a.p63) END p63,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p64) ELSE SUM(a.p64) END p64,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p65) ELSE SUM(a.p65) END p65,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p66) ELSE SUM(a.p66) END p66,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p67) ELSE SUM(a.p67) END p67,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p68) ELSE SUM(a.p68) END p68,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p69) ELSE SUM(a.p69) END p69,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p70) ELSE SUM(a.p70) END p70,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p71) ELSE SUM(a.p71) END p71,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p72) ELSE SUM(a.p72) END p72,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p73) ELSE SUM(a.p73) END p73,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p74) ELSE SUM(a.p74) END p74,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p75) ELSE SUM(a.p75) END p75,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p76) ELSE SUM(a.p76) END p76,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p77) ELSE SUM(a.p77) END p77,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p78) ELSE SUM(a.p78) END p78,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p79) ELSE SUM(a.p79) END p79,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p80) ELSE SUM(a.p80) END p80,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p81) ELSE SUM(a.p81) END p81,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p82) ELSE SUM(a.p82) END p82,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p83) ELSE SUM(a.p83) END p83,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p84) ELSE SUM(a.p84) END p84,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p85) ELSE SUM(a.p85) END p85,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p86) ELSE SUM(a.p86) END p86,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p87) ELSE SUM(a.p87) END p87,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p88) ELSE SUM(a.p88) END p88,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p89) ELSE SUM(a.p89) END p89,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p90) ELSE SUM(a.p90) END p90,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p91) ELSE SUM(a.p91) END p91,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p92) ELSE SUM(a.p92) END p92,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p93) ELSE SUM(a.p93) END p93,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p94) ELSE SUM(a.p94) END p94,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p95) ELSE SUM(a.p95) END p95,
        CASE WHEN a.curve_type = 'DF' THEN AVG(a.p96) ELSE SUM(a.p96) END p96

        FROM `dwd_ml_trade_link_spot_curve` a
        left join sys_dict_code c on a.curve_type = c.dict_code
        where curve_date = #{curveDate} and c.dict_type = 'zcq' and trade_unit_code = #{unitCode}
        <if test="curveType != null">
            and a.curve_type = #{curveType}
        </if>
        GROUP BY a.curve_type,c.dict_label,a.curve_name
    </select>


</mapper>
