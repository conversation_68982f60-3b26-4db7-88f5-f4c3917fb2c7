<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bobandata.cloud.trade.elec.dal.mysql.cons.IntendedCustomersMapper">

    <select id="getConsName" resultType="java.util.Map">
        select * from (
        select caption,GROUP_CONCAT(cons_no) cons,GROUP_CONCAT(id) ids  from corp_cons
        group by caption
        ) a
        where 1 = 1
        <if test="key != null and key != ''">
          and  instr(a.caption,#{key})>0 or instr(a.cons,#{key})>0
        </if>
    </select>

</mapper>
