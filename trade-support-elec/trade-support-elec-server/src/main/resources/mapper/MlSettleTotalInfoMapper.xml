<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bobandata.cloud.trade.elec.dal.mysql.settle.MlSettleTotalInfoMapper">

    <select id="listByMonth"
            resultType="com.bobandata.cloud.trade.elec.dal.dataobject.settle.MlSettleTotalInfoDo">
        select a.*,b.element_name elementName,case when settle_energy = 0 then 0 else round(settle_fee/settle_energy,2) end settlePrice from pmos_settle_info_total a left join mos_element_map b on a.element_id = b.element_id
        where 1 = 1
        <if test="participantId != null and participantId != ''">
            and a.participant_id = #{participantId}
        </if>
        <if test="participantId == null or participantId == ''">
            <if test="groupId != null and groupId != ''">
                and a.participant_id in (select id from glbeco_participant where GROUP_ID = #{groupId})
            </if>
        </if>
        <if test="date != null and date != ''">
            and data_time = #{date}
        </if>
    </select>

    <select id="listSettleDetail"
            resultType="com.bobandata.cloud.trade.elec.dal.dataobject.settle.MlSettledetailDo">
        select *
        from pmos_settle_detail_detailed
        where settle_id = #{settleId}
    </select>
    <select id="dailyClearing" resultType="java.util.LinkedHashMap">
        select '实时上网scada电力' as data_type,
        round(sum(power_3),2) value3,
        round(sum(power_6),2) value6,
        round(sum(power_9),2) value9,
        round(sum(power_12),2) value12,
        round(sum(power_15),2) value15,
        round(sum(power_18),2) value18,
        round(sum(power_21),2) value21,
        round(sum(power_24),2) value24,
        round(sum(power_27),2) value27,
        round(sum(power_30),2) value30,
        round(sum(power_33),2) value33,
        round(sum(power_36),2) value36,
        round(sum(power_39),2) value39,
        round(sum(power_42),2) value42,
        round(sum(power_45),2) value45,
        round(sum(power_48),2) value48,
        round(sum(power_51),2) value51,
        round(sum(power_54),2) value54,
        round(sum(power_57),2) value57,
        round(sum(power_60),2) value60,
        round(sum(power_63),2) value63,
        round(sum(power_66),2) value66,
        round(sum(power_69),2) value69,
        round(sum(power_72),2) value72,
        round(sum(power_75),2) value75,
        round(sum(power_78),2) value78,
        round(sum(power_81),2) value81,
        round(sum(power_84),2) value84,
        round(sum(power_87),2) value87,
        round(sum(power_90),2) value90,
        round(sum(power_93),2) value93,
        round(sum(power_96),2) value96,
        round(sum(power_99),2) value99,
        round(sum(power_102),2) value102,
        round(sum(power_105),2) value105,
        round(sum(power_108),2) value108,
        round(sum(power_111),2) value111,
        round(sum(power_114),2) value114,
        round(sum(power_117),2) value117,
        round(sum(power_120),2) value120,
        round(sum(power_123),2) value123,
        round(sum(power_126),2) value126,
        round(sum(power_129),2) value129,
        round(sum(power_132),2) value132,
        round(sum(power_135),2) value135,
        round(sum(power_138),2) value138,
        round(sum(power_141),2) value141,
        round(sum(power_144),2) value144,
        round(sum(power_147),2) value147,
        round(sum(power_150),2) value150,
        round(sum(power_153),2) value153,
        round(sum(power_156),2) value156,
        round(sum(power_159),2) value159,
        round(sum(power_162),2) value162,
        round(sum(power_165),2) value165,
        round(sum(power_168),2) value168,
        round(sum(power_171),2) value171,
        round(sum(power_174),2) value174,
        round(sum(power_177),2) value177,
        round(sum(power_180),2) value180,
        round(sum(power_183),2) value183,
        round(sum(power_186),2) value186,
        round(sum(power_189),2) value189,
        round(sum(power_192),2) value192,
        round(sum(power_195),2) value195,
        round(sum(power_198),2) value198,
        round(sum(power_201),2) value201,
        round(sum(power_204),2) value204,
        round(sum(power_207),2) value207,
        round(sum(power_210),2) value210,
        round(sum(power_213),2) value213,
        round(sum(power_216),2) value216,
        round(sum(power_219),2) value219,
        round(sum(power_222),2) value222,
        round(sum(power_225),2) value225,
        round(sum(power_228),2) value228,
        round(sum(power_231),2) value231,
        round(sum(power_234),2) value234,
        round(sum(power_237),2) value237,
        round(sum(power_240),2) value240,
        round(sum(power_243),2) value243,
        round(sum(power_246),2) value246,
        round(sum(power_249),2) value249,
        round(sum(power_252),2) value252,
        round(sum(power_255),2) value255,
        round(sum(power_258),2) value258,
        round(sum(power_261),2) value261,
        round(sum(power_264),2) value264,
        round(sum(power_267),2) value267,
        round(sum(power_270),2) value270,
        round(sum(power_273),2) value273,
        round(sum(power_276),2) value276,
        round(sum(power_279),2) value279,
        round(sum(power_282),2) value282,
        round(sum(power_285),2) value285,
        round(sum(power_288),2) value288
        from ( SELECT a.ID, a.PARTICIPANT_ID FROM glbeco_phyunit a where 1=1
        <if test="participantId != null and participantId != ''">
            and a.participant_id = #{participantId}
        </if>
        <if test="participantId == null or participantId == ''">
            <if test="groupId != null and groupId != ''">
                and a.participant_id in (select id from glbeco_participant where GROUP_ID = #{groupId})
            </if>
        </if>
        ) c
        JOIN mos_unit_tmr_scada_power b ON b.phyunit_id = c.ID
        and b.data_time between #{startTime} and #{endTime}
        UNION all
        select
        (case case_type
        when '195' then '双边出清'
        when '200' then '实时计划'
        when '220' then '可靠性计划'
        when '222' then '预平衡计划'
        else null end) as data_type,
        round(sum(p3),2) as value3,round(sum(p6),2) as value6,round(sum(p9),2) as value9,round(sum(p12),2) as
        value12,round(sum(p15),2) as value15,round(sum(p18),2) as value18,round(sum(p21),2) as value21,round(sum(p24),2)
        as value24 ,round(sum(p27),2) as value27,round(sum(p30),2) as value30,round(sum(p33),2) as
        value33,round(sum(p36),2) as value36,round(sum(p39),2) as value39,round(sum(p42),2) as value42,round(sum(p45),2)
        as value45 ,round(sum(p48),2) as value48,round(sum(p51),2) as value51 ,round(sum(p54),2) as
        value54,round(sum(p57),2) as value57,round(sum(p60),2) as value60,round(sum(p63),2) as value63,round(sum(p66),2)
        as value66,round(sum(p69),2) as value69,round(sum(p72),2) as value72,round(sum(p75),2) as
        value75,round(sum(p78),2) as value78 ,round(sum(p81),2) as value81 ,round(sum(p84),2) as value84
        ,round(sum(p87),2) as value87 ,round(sum(p90),2) as value90 ,round(sum(p93),2) as value93 ,round(sum(p96),2) as
        value96 ,round(sum(p99),2) as value99 ,round(sum(p102),2) as value102,round(sum(p105),2) as value105
        ,round(sum(p108),2) as value108 ,round(sum(p111),2) as value111 ,round(sum(p114),2) as value114
        ,round(sum(p117),2) as value117 ,round(sum(p120),2) as value120 ,round(sum(p123),2) as value123
        ,round(sum(p126),2) as value126 ,round(sum(p129),2) as value129 ,round(sum(p132),2) as
        value132,round(sum(p135),2) as value135 ,round(sum(p138),2) as value138 ,round(sum(p141),2) as value141
        ,round(sum(p144),2) as value144 ,round(sum(p147),2) as value147 ,round(sum(p150),2) as value150
        ,round(sum(p153),2) as value153 ,round(sum(p156),2) as value156 ,round(sum(p159),2) as value159
        ,round(sum(p162),2) as value162 ,round(sum(p165),2) as value165 ,round(sum(p168),2) as value168
        ,round(sum(p171),2) as value171 ,round(sum(p174),2) as value174 ,round(sum(p177),2) as value177
        ,round(sum(p180),2) as value180 ,round(sum(p183),2) as value183 ,round(sum(p186),2) as value186
        ,round(sum(p189),2) as value189 ,round(sum(p192),2) as value192 ,round(sum(p195),2) as value195
        ,round(sum(p198),2) as value198 ,round(sum(p201),2) as value201 ,round(sum(p204),2) as value204
        ,round(sum(p207),2) as value207 ,round(sum(p210),2) as value210 ,round(sum(p213),2) as value213
        ,round(sum(p216),2) as value216 ,round(sum(p219),2) as value219 ,round(sum(p222),2) as value222
        ,round(sum(p225),2) as value225 ,round(sum(p228),2) as value228 ,round(sum(p231),2) as value231
        ,round(sum(p234),2) as value234 ,round(sum(p237),2) as value237 ,round(sum(p240),2) as value240
        ,round(sum(p243),2) as value243 ,round(sum(p246),2) as value246 ,round(sum(p249),2) as value249
        ,round(sum(p252),2) as value252 ,round(sum(p255),2) as value255 ,round(sum(p258),2) as value258
        ,round(sum(p261),2) as value261 ,round(sum(p264),2) as value264 ,round(sum(p267),2) as value267
        ,round(sum(p270),2) as value270 ,round(sum(p273),2) as value273 ,round(sum(p276),2) as value276
        ,round(sum(p279),2) as value279 ,round(sum(p282),2) as value282 ,round(sum(p285),2) as value285
        ,round(sum(p288),2) as value288 FROM
        mos_unit_pre_plan b where 1 = 1
        <if test="participantId != null and participantId != ''">
            and participant_id = #{participantId}
        </if>
        <if test="participantId == null or participantId == ''">
            <if test="groupId != null and groupId != ''">
                and participant_id in (select id from glbeco_participant where GROUP_ID = #{groupId})
            </if>
        </if>
        AND b.data_time between #{startTime} and #{endTime} and b.case_type in (195,200,220,222)
        GROUP BY
        b.CASE_TYPE
        UNION all
        SELECT
        '自计划' as data_type,
        round(sum(IF ( P3= - 9999, NULL, P3)),2) as value3,
        round(sum(IF ( P6= - 9999, NULL, P6)),2) as value6,
        round(sum(IF ( P9= - 9999, NULL, P9)),2) as value9,
        round(sum(IF ( P12= - 9999, NULL, P12)),2) as value12,
        round(sum(IF ( P15= - 9999, NULL, P15)),2) as value15,
        round(sum(IF ( P18= - 9999, NULL, P18)),2) as value18,
        round(sum(IF ( P21= - 9999, NULL, P21)),2) as value21,
        round(sum(IF ( P24= - 9999, NULL, P24)),2) as value24,
        round(sum(IF ( P27= - 9999, NULL, P27)),2) as value27,
        round(sum(IF ( P30= - 9999, NULL, P30)),2) as value30,
        round(sum(IF ( P33= - 9999, NULL, P33)),2) as value33,
        round(sum(IF ( P36= - 9999, NULL, P36)),2) as value36,
        round(sum(IF ( P39= - 9999, NULL, P39)),2) as value39,
        round(sum(IF ( P42= - 9999, NULL, P42)),2) as value42,
        round(sum(IF ( P45= - 9999, NULL, P45)),2) as value45,
        round(sum(IF ( P48= - 9999, NULL, P48)),2) as value48,
        round(sum(IF ( P51= - 9999, NULL, P51)),2) as value51,
        round(sum(IF ( P54= - 9999, NULL, P54)),2) as value54,
        round(sum(IF ( P57= - 9999, NULL, P57)),2) as value57,
        round(sum(IF ( P60= - 9999, NULL, P60)),2) as value60,
        round(sum(IF ( P63= - 9999, NULL, P63)),2) as value63,
        round(sum(IF ( P66= - 9999, NULL, P66)),2) as value66,
        round(sum(IF ( P69= - 9999, NULL, P69)),2) as value69,
        round(sum(IF ( P72= - 9999, NULL, P72)),2) as value72,
        round(sum(IF ( P75= - 9999, NULL, P75)),2) as value75,
        round(sum(IF ( P78= - 9999, NULL, P78)),2) as value78,
        round(sum(IF ( P81= - 9999, NULL, P81)),2) as value81,
        round(sum(IF ( P84= - 9999, NULL, P84)),2) as value84,
        round(sum(IF ( P87= - 9999, NULL, P87)),2) as value87,
        round(sum(IF ( P90= - 9999, NULL, P90)),2) as value90,
        round(sum(IF ( P93= - 9999, NULL, P93)),2) as value93,
        round(sum(IF ( P96= - 9999, NULL, P96)),2) as value96,
        round(sum(IF ( P99= - 9999, NULL, P99)),2) as value99,
        round(sum(IF ( P102= - 9999, NULL, P102)),2) as value102,
        round(sum(IF ( P105= - 9999, NULL, P105)),2) as value105,
        round(sum(IF ( P108= - 9999, NULL, P108)),2) as value108,
        round(sum(IF ( P111= - 9999, NULL, P111)),2) as value111,
        round(sum(IF ( P114= - 9999, NULL, P114)),2) as value114,
        round(sum(IF ( P117= - 9999, NULL, P117)),2) as value117,
        round(sum(IF ( P120= - 9999, NULL, P120)),2) as value120,
        round(sum(IF ( P123= - 9999, NULL, P123)),2) as value123,
        round(sum(IF ( P126= - 9999, NULL, P126)),2) as value126,
        round(sum(IF ( P129= - 9999, NULL, P129)),2) as value129,
        round(sum(IF ( P132= - 9999, NULL, P132)),2) as value132,
        round(sum(IF ( P135= - 9999, NULL, P135)),2) as value135,
        round(sum(IF ( P138= - 9999, NULL, P138)),2) as value138,
        round(sum(IF ( P141= - 9999, NULL, P141)),2) as value141,
        round(sum(IF ( P144= - 9999, NULL, P144)),2) as value144,
        round(sum(IF ( P147= - 9999, NULL, P147)),2) as value147,
        round(sum(IF ( P150= - 9999, NULL, P150)),2) as value150,
        round(sum(IF ( P153= - 9999, NULL, P153)),2) as value153,
        round(sum(IF ( P156= - 9999, NULL, P156)),2) as value156,
        round(sum(IF ( P159= - 9999, NULL, P159)),2) as value159,
        round(sum(IF ( P162= - 9999, NULL, P162)),2) as value162,
        round(sum(IF ( P165= - 9999, NULL, P165)),2) as value165,
        round(sum(IF ( P168= - 9999, NULL, P168)),2) as value168,
        round(sum(IF ( P171= - 9999, NULL, P171)),2) as value171,
        round(sum(IF ( P174= - 9999, NULL, P174)),2) as value174,
        round(sum(IF ( P177= - 9999, NULL, P177)),2) as value177,
        round(sum(IF ( P180= - 9999, NULL, P180)),2) as value180,
        round(sum(IF ( P183= - 9999, NULL, P183)),2) as value183,
        round(sum(IF ( P186= - 9999, NULL, P186)),2) as value186,
        round(sum(IF ( P189= - 9999, NULL, P189)),2) as value189,
        round(sum(IF ( P192= - 9999, NULL, P192)),2) as value192,
        round(sum(IF ( P195= - 9999, NULL, P195)),2) as value195,
        round(sum(IF ( P198= - 9999, NULL, P198)),2) as value198,
        round(sum(IF ( P201= - 9999, NULL, P201)),2) as value201,
        round(sum(IF ( P204= - 9999, NULL, P204)),2) as value204,
        round(sum(IF ( P207= - 9999, NULL, P207)),2) as value207,
        round(sum(IF ( P210= - 9999, NULL, P210)),2) as value210,
        round(sum(IF ( P213= - 9999, NULL, P213)),2) as value213,
        round(sum(IF ( P216= - 9999, NULL, P216)),2) as value216,
        round(sum(IF ( P219= - 9999, NULL, P219)),2) as value219,
        round(sum(IF ( P222= - 9999, NULL, P222)),2) as value222,
        round(sum(IF ( P225= - 9999, NULL, P225)),2) as value225,
        round(sum(IF ( P228= - 9999, NULL, P228)),2) as value228,
        round(sum(IF ( P231= - 9999, NULL, P231)),2) as value231,
        round(sum(IF ( P234= - 9999, NULL, P234)),2) as value234,
        round(sum(IF ( P237= - 9999, NULL, P237)),2) as value237,
        round(sum(IF ( P240= - 9999, NULL, P240)),2) as value240,
        round(sum(IF ( P243= - 9999, NULL, P243)),2) as value243,
        round(sum(IF ( P246= - 9999, NULL, P246)),2) as value246,
        round(sum(IF ( P249= - 9999, NULL, P249)),2) as value249,
        round(sum(IF ( P252= - 9999, NULL, P252)),2) as value252,
        round(sum(IF ( P255= - 9999, NULL, P255)),2) as value255,
        round(sum(IF ( P258= - 9999, NULL, P258)),2) as value258,
        round(sum(IF ( P261= - 9999, NULL, P261)),2) as value261,
        round(sum(IF ( P264= - 9999, NULL, P264)),2) as value264,
        round(sum(IF ( P267= - 9999, NULL, P267)),2) as value267,
        round(sum(IF ( P270= - 9999, NULL, P270)),2) as value270,
        round(sum(IF ( P273= - 9999, NULL, P273)),2) as value273,
        round(sum(IF ( P276= - 9999, NULL, P276)),2) as value276,
        round(sum(IF ( P279= - 9999, NULL, P279)),2) as value279,
        round(sum(IF ( P282= - 9999, NULL, P282)),2) as value282,
        round(sum(IF ( P285= - 9999, NULL, P285)),2) as value285,
        round(sum(IF ( P288= - 9999, NULL, P288)),2) as value288
        FROM
        mos_plant_self_schedule where 1=1
        <if test="participantId != null and participantId != ''">
            and participant_id = #{participantId}
        </if>
        <if test="participantId == null or participantId == ''">
            <if test="groupId != null and groupId != ''">
                and participant_id in (select id from glbeco_participant where GROUP_ID = #{groupId})
            </if>
        </if>
        and data_time between #{startTime} and #{endTime}
        UNION all
        SELECT
        (case case_type
        when '200' then '超短期预测'
        when '220' then '短期预测'
        else null end) as data_type,round(sum(p3),2) as value3,round(sum(p6),2) as value6,round(sum(p9),2) as
        value9,round(sum(p12),2) as value12,round(sum(p15),2) as value15,round(sum(p18),2) as value18,round(sum(p21),2)
        as value21,round(sum(p24),2) as value24 ,round(sum(p27),2) as value27,round(sum(p30),2) as
        value30,round(sum(p33),2) as value33,round(sum(p36),2) as value36,round(sum(p39),2) as value39,round(sum(p42),2)
        as value42,round(sum(p45),2) as value45 ,round(sum(p48),2) as value48,round(sum(p51),2) as value51
        ,round(sum(p54),2) as value54,round(sum(p57),2) as value57,round(sum(p60),2) as value60,round(sum(p63),2) as
        value63,round(sum(p66),2) as value66,round(sum(p69),2) as value69,round(sum(p72),2) as value72,round(sum(p75),2)
        as value75,round(sum(p78),2) as value78 ,round(sum(p81),2) as value81 ,round(sum(p84),2) as value84
        ,round(sum(p87),2) as value87 ,round(sum(p90),2) as value90 ,round(sum(p93),2) as value93 ,round(sum(p96),2) as
        value96 ,round(sum(p99),2) as value99 ,round(sum(p102),2) as value102,round(sum(p105),2) as value105
        ,round(sum(p108),2) as value108 ,round(sum(p111),2) as value111 ,round(sum(p114),2) as value114
        ,round(sum(p117),2) as value117 ,round(sum(p120),2) as value120 ,round(sum(p123),2) as value123
        ,round(sum(p126),2) as value126 ,round(sum(p129),2) as value129 ,round(sum(p132),2) as
        value132,round(sum(p135),2) as value135 ,round(sum(p138),2) as value138 ,round(sum(p141),2) as value141
        ,round(sum(p144),2) as value144 ,round(sum(p147),2) as value147 ,round(sum(p150),2) as value150
        ,round(sum(p153),2) as value153 ,round(sum(p156),2) as value156 ,round(sum(p159),2) as value159
        ,round(sum(p162),2) as value162 ,round(sum(p165),2) as value165 ,round(sum(p168),2) as value168
        ,round(sum(p171),2) as value171 ,round(sum(p174),2) as value174 ,round(sum(p177),2) as value177
        ,round(sum(p180),2) as value180 ,round(sum(p183),2) as value183 ,round(sum(p186),2) as value186
        ,round(sum(p189),2) as value189 ,round(sum(p192),2) as value192 ,round(sum(p195),2) as value195
        ,round(sum(p198),2) as value198 ,round(sum(p201),2) as value201 ,round(sum(p204),2) as value204
        ,round(sum(p207),2) as value207 ,round(sum(p210),2) as value210 ,round(sum(p213),2) as value213
        ,round(sum(p216),2) as value216 ,round(sum(p219),2) as value219 ,round(sum(p222),2) as value222
        ,round(sum(p225),2) as value225 ,round(sum(p228),2) as value228 ,round(sum(p231),2) as value231
        ,round(sum(p234),2) as value234 ,round(sum(p237),2) as value237 ,round(sum(p240),2) as value240
        ,round(sum(p243),2) as value243 ,round(sum(p246),2) as value246 ,round(sum(p249),2) as value249
        ,round(sum(p252),2) as value252 ,round(sum(p255),2) as value255 ,round(sum(p258),2) as value258
        ,round(sum(p261),2) as value261 ,round(sum(p264),2) as value264 ,round(sum(p267),2) as value267
        ,round(sum(p270),2) as value270 ,round(sum(p273),2) as value273 ,round(sum(p276),2) as value276
        ,round(sum(p279),2) as value279 ,round(sum(p282),2) as value282 ,round(sum(p285),2) as value285
        ,round(sum(p288),2) as value288 FROM
        ( SELECT a.ID,a.PARTICIPANT_ID FROM glbeco_phyunit a where 1=1
        <if test="participantId != null and participantId != ''">
            and a.participant_id = #{participantId}
        </if>
        <if test="participantId == null or participantId == ''">
            <if test="groupId != null and groupId != ''">
                and a.participant_id in (select id from glbeco_participant where GROUP_ID = #{groupId})
            </if>
        </if>
        ) c
        JOIN mos_unit_newenergy_fore b ON b.phyunit_id = c.ID
        AND b.DATA_TIME between #{startTime} and #{endTime} and b.CASE_TYPE in(200,220)
        GROUP BY
        b.CASE_TYPE
        union all
        select '实时出清依据',
        sum(value3) value3,
        sum(value6) value6,
        sum(value9) value9,
        sum(value12) value12,
        sum(value15) value15,
        sum(value18) value18,
        sum(value21) value21,
        sum(value24) value24,
        sum(value27) value27,
        sum(value30) value30,
        sum(value33) value33,
        sum(value36) value36,
        sum(value39) value39,
        sum(value42) value42,
        sum(value45) value45,
        sum(value48) value48,
        sum(value51) value51,
        sum(value54) value54,
        sum(value57) value57,
        sum(value60) value60,
        sum(value63) value63,
        sum(value66) value66,
        sum(value69) value69,
        sum(value72) value72,
        sum(value75) value75,
        sum(value78) value78,
        sum(value81) value81,
        sum(value84) value84,
        sum(value87) value87,
        sum(value90) value90,
        sum(value93) value93,
        sum(value96) value96,
        sum(value99) value99,
        sum(value102) value102,
        sum(value105) value105,
        sum(value108) value108,
        sum(value111) value111,
        sum(value114) value114,
        sum(value117) value117,
        sum(value120) value120,
        sum(value123) value123,
        sum(value126) value126,
        sum(value129) value129,
        sum(value132) value132,
        sum(value135) value135,
        sum(value138) value138,
        sum(value141) value141,
        sum(value144) value144,
        sum(value147) value147,
        sum(value150) value150,
        sum(value153) value153,
        sum(value156) value156,
        sum(value159) value159,
        sum(value162) value162,
        sum(value165) value165,
        sum(value168) value168,
        sum(value171) value171,
        sum(value174) value174,
        sum(value177) value177,
        sum(value180) value180,
        sum(value183) value183,
        sum(value186) value186,
        sum(value189) value189,
        sum(value192) value192,
        sum(value195) value195,
        sum(value198) value198,
        sum(value201) value201,
        sum(value204) value204,
        sum(value207) value207,
        sum(value210) value210,
        sum(value213) value213,
        sum(value216) value216,
        sum(value219) value219,
        sum(value222) value222,
        sum(value225) value225,
        sum(value228) value228,
        sum(value231) value231,
        sum(value234) value234,
        sum(value237) value237,
        sum(value240) value240,
        sum(value243) value243,
        sum(value246) value246,
        sum(value249) value249,
        sum(value252) value252,
        sum(value255) value255,
        sum(value258) value258,
        sum(value261) value261,
        sum(value264) value264,
        sum(value267) value267,
        sum(value270) value270,
        sum(value273) value273,
        sum(value276) value276,
        sum(value279) value279,
        sum(value282) value282,
        sum(value285) value285,
        sum(value288) value288
        from (
        select a.data_time,a.PARTICIPANT_ID,
        round(IFNULL(IF ( b.value3= - 9999, NULL, b.value3),a.value3),2) as value3,
        round(IFNULL(IF ( b.value6= - 9999, NULL, b.value6),a.value6),2) as value6,
        round(IFNULL(IF ( b.value9= - 9999, NULL, b.value9),a.value9),2) as value9,
        round(IFNULL(IF ( b.value12= - 9999, NULL, b.value12),a.value12),2) as value12,
        round(IFNULL(IF ( b.value15= - 9999, NULL, b.value15),a.value15),2) as value15,
        round(IFNULL(IF ( b.value18= - 9999, NULL, b.value18),a.value18),2) as value18,
        round(IFNULL(IF ( b.value21= - 9999, NULL, b.value21),a.value21),2) as value21,
        round(IFNULL(IF ( b.value24= - 9999, NULL, b.value24),a.value24),2) as value24,
        round(IFNULL(IF ( b.value27= - 9999, NULL, b.value27),a.value27),2) as value27,
        round(IFNULL(IF ( b.value30= - 9999, NULL, b.value30),a.value30),2) as value30,
        round(IFNULL(IF ( b.value33= - 9999, NULL, b.value33),a.value33),2) as value33,
        round(IFNULL(IF ( b.value36= - 9999, NULL, b.value36),a.value36),2) as value36,
        round(IFNULL(IF ( b.value39= - 9999, NULL, b.value39),a.value39),2) as value39,
        round(IFNULL(IF ( b.value42= - 9999, NULL, b.value42),a.value42),2) as value42,
        round(IFNULL(IF ( b.value45= - 9999, NULL, b.value45),a.value45),2) as value45,
        round(IFNULL(IF ( b.value48= - 9999, NULL, b.value48),a.value48),2) as value48,
        round(IFNULL(IF ( b.value51= - 9999, NULL, b.value51),a.value51),2) as value51,
        round(IFNULL(IF ( b.value54= - 9999, NULL, b.value54),a.value54),2) as value54,
        round(IFNULL(IF ( b.value57= - 9999, NULL, b.value57),a.value57),2) as value57,
        round(IFNULL(IF ( b.value60= - 9999, NULL, b.value60),a.value60),2) as value60,
        round(IFNULL(IF ( b.value63= - 9999, NULL, b.value63),a.value63),2) as value63,
        round(IFNULL(IF ( b.value66= - 9999, NULL, b.value66),a.value66),2) as value66,
        round(IFNULL(IF ( b.value69= - 9999, NULL, b.value69),a.value69),2) as value69,
        round(IFNULL(IF ( b.value72= - 9999, NULL, b.value72),a.value72),2) as value72,
        round(IFNULL(IF ( b.value75= - 9999, NULL, b.value75),a.value75),2) as value75,
        round(IFNULL(IF ( b.value78= - 9999, NULL, b.value78),a.value78),2) as value78,
        round(IFNULL(IF ( b.value81= - 9999, NULL, b.value81),a.value81),2) as value81,
        round(IFNULL(IF ( b.value84= - 9999, NULL, b.value84),a.value84),2) as value84,
        round(IFNULL(IF ( b.value87= - 9999, NULL, b.value87),a.value87),2) as value87,
        round(IFNULL(IF ( b.value90= - 9999, NULL, b.value90),a.value90),2) as value90,
        round(IFNULL(IF ( b.value93= - 9999, NULL, b.value93),a.value93),2) as value93,
        round(IFNULL(IF ( b.value96= - 9999, NULL, b.value96),a.value96),2) as value96,
        round(IFNULL(IF ( b.value99= - 9999, NULL, b.value99),a.value99),2) as value99,
        round(IFNULL(IF ( b.value102= - 9999, NULL, b.value102),a.value102),2) as value102,
        round(IFNULL(IF ( b.value105= - 9999, NULL, b.value105),a.value105),2) as value105,
        round(IFNULL(IF ( b.value108= - 9999, NULL, b.value108),a.value108),2) as value108,
        round(IFNULL(IF ( b.value111= - 9999, NULL, b.value111),a.value111),2) as value111,
        round(IFNULL(IF ( b.value114= - 9999, NULL, b.value114),a.value114),2) as value114,
        round(IFNULL(IF ( b.value117= - 9999, NULL, b.value117),a.value117),2) as value117,
        round(IFNULL(IF ( b.value120= - 9999, NULL, b.value120),a.value120),2) as value120,
        round(IFNULL(IF ( b.value123= - 9999, NULL, b.value123),a.value123),2) as value123,
        round(IFNULL(IF ( b.value126= - 9999, NULL, b.value126),a.value126),2) as value126,
        round(IFNULL(IF ( b.value129= - 9999, NULL, b.value129),a.value129),2) as value129,
        round(IFNULL(IF ( b.value132= - 9999, NULL, b.value132),a.value132),2) as value132,
        round(IFNULL(IF ( b.value135= - 9999, NULL, b.value135),a.value135),2) as value135,
        round(IFNULL(IF ( b.value138= - 9999, NULL, b.value138),a.value138),2) as value138,
        round(IFNULL(IF ( b.value141= - 9999, NULL, b.value141),a.value141),2) as value141,
        round(IFNULL(IF ( b.value144= - 9999, NULL, b.value144),a.value144),2) as value144,
        round(IFNULL(IF ( b.value147= - 9999, NULL, b.value147),a.value147),2) as value147,
        round(IFNULL(IF ( b.value150= - 9999, NULL, b.value150),a.value150),2) as value150,
        round(IFNULL(IF ( b.value153= - 9999, NULL, b.value153),a.value153),2) as value153,
        round(IFNULL(IF ( b.value156= - 9999, NULL, b.value156),a.value156),2) as value156,
        round(IFNULL(IF ( b.value159= - 9999, NULL, b.value159),a.value159),2) as value159,
        round(IFNULL(IF ( b.value162= - 9999, NULL, b.value162),a.value162),2) as value162,
        round(IFNULL(IF ( b.value165= - 9999, NULL, b.value165),a.value165),2) as value165,
        round(IFNULL(IF ( b.value168= - 9999, NULL, b.value168),a.value168),2) as value168,
        round(IFNULL(IF ( b.value171= - 9999, NULL, b.value171),a.value171),2) as value171,
        round(IFNULL(IF ( b.value174= - 9999, NULL, b.value174),a.value174),2) as value174,
        round(IFNULL(IF ( b.value177= - 9999, NULL, b.value177),a.value177),2) as value177,
        round(IFNULL(IF ( b.value180= - 9999, NULL, b.value180),a.value180),2) as value180,
        round(IFNULL(IF ( b.value183= - 9999, NULL, b.value183),a.value183),2) as value183,
        round(IFNULL(IF ( b.value186= - 9999, NULL, b.value186),a.value186),2) as value186,
        round(IFNULL(IF ( b.value189= - 9999, NULL, b.value189),a.value189),2) as value189,
        round(IFNULL(IF ( b.value192= - 9999, NULL, b.value192),a.value192),2) as value192,
        round(IFNULL(IF ( b.value195= - 9999, NULL, b.value195),a.value195),2) as value195,
        round(IFNULL(IF ( b.value198= - 9999, NULL, b.value198),a.value198),2) as value198,
        round(IFNULL(IF ( b.value201= - 9999, NULL, b.value201),a.value201),2) as value201,
        round(IFNULL(IF ( b.value204= - 9999, NULL, b.value204),a.value204),2) as value204,
        round(IFNULL(IF ( b.value207= - 9999, NULL, b.value207),a.value207),2) as value207,
        round(IFNULL(IF ( b.value210= - 9999, NULL, b.value210),a.value210),2) as value210,
        round(IFNULL(IF ( b.value213= - 9999, NULL, b.value213),a.value213),2) as value213,
        round(IFNULL(IF ( b.value216= - 9999, NULL, b.value216),a.value216),2) as value216,
        round(IFNULL(IF ( b.value219= - 9999, NULL, b.value219),a.value219),2) as value219,
        round(IFNULL(IF ( b.value222= - 9999, NULL, b.value222),a.value222),2) as value222,
        round(IFNULL(IF ( b.value225= - 9999, NULL, b.value225),a.value225),2) as value225,
        round(IFNULL(IF ( b.value228= - 9999, NULL, b.value228),a.value228),2) as value228,
        round(IFNULL(IF ( b.value231= - 9999, NULL, b.value231),a.value231),2) as value231,
        round(IFNULL(IF ( b.value234= - 9999, NULL, b.value234),a.value234),2) as value234,
        round(IFNULL(IF ( b.value237= - 9999, NULL, b.value237),a.value237),2) as value237,
        round(IFNULL(IF ( b.value240= - 9999, NULL, b.value240),a.value240),2) as value240,
        round(IFNULL(IF ( b.value243= - 9999, NULL, b.value243),a.value243),2) as value243,
        round(IFNULL(IF ( b.value246= - 9999, NULL, b.value246),a.value246),2) as value246,
        round(IFNULL(IF ( b.value249= - 9999, NULL, b.value249),a.value249),2) as value249,
        round(IFNULL(IF ( b.value252= - 9999, NULL, b.value252),a.value252),2) as value252,
        round(IFNULL(IF ( b.value255= - 9999, NULL, b.value255),a.value255),2) as value255,
        round(IFNULL(IF ( b.value258= - 9999, NULL, b.value258),a.value258),2) as value258,
        round(IFNULL(IF ( b.value261= - 9999, NULL, b.value261),a.value261),2) as value261,
        round(IFNULL(IF ( b.value264= - 9999, NULL, b.value264),a.value264),2) as value264,
        round(IFNULL(IF ( b.value267= - 9999, NULL, b.value267),a.value267),2) as value267,
        round(IFNULL(IF ( b.value270= - 9999, NULL, b.value270),a.value270),2) as value270,
        round(IFNULL(IF ( b.value273= - 9999, NULL, b.value273),a.value273),2) as value273,
        round(IFNULL(IF ( b.value276= - 9999, NULL, b.value276),a.value276),2) as value276,
        round(IFNULL(IF ( b.value279= - 9999, NULL, b.value279),a.value279),2) as value279,
        round(IFNULL(IF ( b.value282= - 9999, NULL, b.value282),a.value282),2) as value282,
        round(IFNULL(IF ( b.value285= - 9999, NULL, b.value285),a.value285),2) as value285,
        round(IFNULL(IF ( b.value288= - 9999, NULL, b.value288),a.value288),2) as value288
        from (
        SELECT
        b.PARTICIPANT_ID,b.DATA_TIME,round(sum(p3),2) as value3,round(sum(p6),2) as value6,round(sum(p9),2) as
        value9,round(sum(p12),2) as value12,round(sum(p15),2) as value15,round(sum(p18),2) as value18,round(sum(p21),2)
        as value21,round(sum(p24),2) as value24 ,round(sum(p27),2) as value27,round(sum(p30),2) as
        value30,round(sum(p33),2) as value33,round(sum(p36),2) as value36,round(sum(p39),2) as value39,round(sum(p42),2)
        as value42,round(sum(p45),2) as value45 ,round(sum(p48),2) as value48,round(sum(p51),2) as value51
        ,round(sum(p54),2) as value54,round(sum(p57),2) as value57,round(sum(p60),2) as value60,round(sum(p63),2) as
        value63,round(sum(p66),2) as value66,round(sum(p69),2) as value69,round(sum(p72),2) as value72,round(sum(p75),2)
        as value75,round(sum(p78),2) as value78 ,round(sum(p81),2) as value81 ,round(sum(p84),2) as value84
        ,round(sum(p87),2) as value87 ,round(sum(p90),2) as value90 ,round(sum(p93),2) as value93 ,round(sum(p96),2) as
        value96 ,round(sum(p99),2) as value99 ,round(sum(p102),2) as value102,round(sum(p105),2) as value105
        ,round(sum(p108),2) as value108 ,round(sum(p111),2) as value111 ,round(sum(p114),2) as value114
        ,round(sum(p117),2) as value117 ,round(sum(p120),2) as value120 ,round(sum(p123),2) as value123
        ,round(sum(p126),2) as value126 ,round(sum(p129),2) as value129 ,round(sum(p132),2) as
        value132,round(sum(p135),2) as value135 ,round(sum(p138),2) as value138 ,round(sum(p141),2) as value141
        ,round(sum(p144),2) as value144 ,round(sum(p147),2) as value147 ,round(sum(p150),2) as value150
        ,round(sum(p153),2) as value153 ,round(sum(p156),2) as value156 ,round(sum(p159),2) as value159
        ,round(sum(p162),2) as value162 ,round(sum(p165),2) as value165 ,round(sum(p168),2) as value168
        ,round(sum(p171),2) as value171 ,round(sum(p174),2) as value174 ,round(sum(p177),2) as value177
        ,round(sum(p180),2) as value180 ,round(sum(p183),2) as value183 ,round(sum(p186),2) as value186
        ,round(sum(p189),2) as value189 ,round(sum(p192),2) as value192 ,round(sum(p195),2) as value195
        ,round(sum(p198),2) as value198 ,round(sum(p201),2) as value201 ,round(sum(p204),2) as value204
        ,round(sum(p207),2) as value207 ,round(sum(p210),2) as value210 ,round(sum(p213),2) as value213
        ,round(sum(p216),2) as value216 ,round(sum(p219),2) as value219 ,round(sum(p222),2) as value222
        ,round(sum(p225),2) as value225 ,round(sum(p228),2) as value228 ,round(sum(p231),2) as value231
        ,round(sum(p234),2) as value234 ,round(sum(p237),2) as value237 ,round(sum(p240),2) as value240
        ,round(sum(p243),2) as value243 ,round(sum(p246),2) as value246 ,round(sum(p249),2) as value249
        ,round(sum(p252),2) as value252 ,round(sum(p255),2) as value255 ,round(sum(p258),2) as value258
        ,round(sum(p261),2) as value261 ,round(sum(p264),2) as value264 ,round(sum(p267),2) as value267
        ,round(sum(p270),2) as value270 ,round(sum(p273),2) as value273 ,round(sum(p276),2) as value276
        ,round(sum(p279),2) as value279 ,round(sum(p282),2) as value282 ,round(sum(p285),2) as value285
        ,round(sum(p288),2) as value288 FROM
        mos_unit_newenergy_fore b where 1=1
        <if test="participantId != null and participantId != ''">
            and participant_id = #{participantId}
        </if>
        <if test="participantId == null or participantId == ''">
            <if test="groupId != null and groupId != ''">
                and participant_id in (select id from glbeco_participant where GROUP_ID = #{groupId})
            </if>
        </if>
        AND b.DATA_TIME between #{startTime} and #{endTime} and b.CASE_TYPE = 220
        GROUP BY
        b.PARTICIPANT_ID,b.DATA_TIME
        ) a left join
        (
        SELECT
        PARTICIPANT_ID as participant_id,
        data_time,round(IF ( P3= - 9999, NULL, P3),2) as value3,round(IF ( P6= - 9999, NULL, P6),2) as value6,round(IF (
        P9= - 9999, NULL, P9),2) as value9,round(IF ( P12= - 9999, NULL, P12),2) as value12,round(IF ( P15= - 9999,
        NULL, P15),2) as value15,round(IF ( P18= - 9999, NULL, P18),2) as value18,round(IF ( P21= - 9999, NULL, P21),2)
        as value21,round(IF ( P24= - 9999, NULL, P24),2) as value24 ,round(IF ( P27= - 9999, NULL, P27),2) as
        value27,round(IF ( P30= - 9999, NULL, P30),2) as value30,round(IF ( P33= - 9999, NULL, P33),2) as
        value33,round(IF ( P36= - 9999, NULL, P36),2) as value36,round(IF ( P39= - 9999, NULL, P39),2) as
        value39,round(IF ( P42= - 9999, NULL, P42),2) as value42,round(IF ( P45= - 9999, NULL, P45),2) as value45
        ,round(IF ( P48= - 9999, NULL, P48),2) as value48,round(IF ( P51= - 9999, NULL, P51),2) as value51 ,round(IF (
        P54= - 9999, NULL, P54),2) as value54,round(IF ( P57= - 9999, NULL, P57),2) as value57,round(IF ( P60= - 9999,
        NULL, P60),2) as value60,round(IF ( P63= - 9999, NULL, P63),2) as value63,round(IF ( P66= - 9999, NULL, P66),2)
        as value66,round(IF ( P69= - 9999, NULL, P69),2) as value69,round(IF ( P72= - 9999, NULL, P72),2) as
        value72,round(IF ( P75= - 9999, NULL, P75),2) as value75,round(IF ( P78= - 9999, NULL, P78),2) as value78
        ,round(IF ( P81= - 9999, NULL, P81),2) as value81 ,round(IF ( P84= - 9999, NULL, P84),2) as value84 ,round(IF (
        P87= - 9999, NULL, P87),2) as value87 ,round(IF ( P90= - 9999, NULL, P90),2) as value90 ,round(IF ( P93= - 9999,
        NULL, P93),2) as value93 ,round(IF ( P96= - 9999, NULL, P96),2) as value96 ,round(IF ( P99= - 9999, NULL,
        P99),2) as value99 ,round(IF ( P102= - 9999, NULL, P102),2) as value102 ,round(IF ( P105= - 9999, NULL, P105),2)
        as value105 ,round(IF ( P108= - 9999, NULL, P108),2) as value108 ,round(IF ( P111= - 9999, NULL, P111),2) as
        value111 ,round(IF ( P114= - 9999, NULL, P114),2) as value114 ,round(IF ( P117= - 9999, NULL, P117),2) as
        value117 ,round(IF ( P120= - 9999, NULL, P120),2) as value120 ,round(IF ( P123= - 9999, NULL, P123),2) as
        value123 ,round(IF ( P126= - 9999, NULL, P126),2) as value126 ,round(IF ( P129= - 9999, NULL, P129),2) as
        value129 ,round(IF ( P132= - 9999, NULL, P132),2) as value132 ,round(IF ( P135= - 9999, NULL, P135),2) as
        value135 ,round(IF ( P138= - 9999, NULL, P138),2) as value138 ,round(IF ( P141= - 9999, NULL, P141),2) as
        value141 ,round(IF ( P144= - 9999, NULL, P144),2) as value144 ,round(IF ( P147= - 9999, NULL, P147),2) as
        value147 ,round(IF ( P150= - 9999, NULL, P150),2) as value150 ,round(IF ( P153= - 9999, NULL, P153),2) as
        value153 ,round(IF ( P156= - 9999, NULL, P156),2) as value156 ,round(IF ( P159= - 9999, NULL, P159),2) as
        value159 ,round(IF ( P162= - 9999, NULL, P162),2) as value162 ,round(IF ( P165= - 9999, NULL, P165),2) as
        value165 ,round(IF ( P168= - 9999, NULL, P168),2) as value168 ,round(IF ( P171= - 9999, NULL, P171),2) as
        value171 ,round(IF ( P174= - 9999, NULL, P174),2) as value174 ,round(IF ( P177= - 9999, NULL, P177),2) as
        value177 ,round(IF ( P180= - 9999, NULL, P180),2) as value180 ,round(IF ( P183= - 9999, NULL, P183),2) as
        value183 ,round(IF ( P186= - 9999, NULL, P186),2) as value186 ,round(IF ( P189= - 9999, NULL, P189),2) as
        value189 ,round(IF ( P192= - 9999, NULL, P192),2) as value192 ,round(IF ( P195= - 9999, NULL, P195),2) as
        value195 ,round(IF ( P198= - 9999, NULL, P198),2) as value198 ,round(IF ( P201= - 9999, NULL, P201),2) as
        value201 ,round(IF ( P204= - 9999, NULL, P204),2) as value204 ,round(IF ( P207= - 9999, NULL, P207),2) as
        value207 ,round(IF ( P210= - 9999, NULL, P210),2) as value210 ,round(IF ( P213= - 9999, NULL, P213),2) as
        value213 ,round(IF ( P216= - 9999, NULL, P216),2) as value216 ,round(IF ( P219= - 9999, NULL, P219),2) as
        value219 ,round(IF ( P222= - 9999, NULL, P222),2) as value222 ,round(IF ( P225= - 9999, NULL, P225),2) as
        value225 ,round(IF ( P228= - 9999, NULL, P228),2) as value228 ,round(IF ( P231= - 9999, NULL, P231),2) as
        value231 ,round(IF ( P234= - 9999, NULL, P234),2) as value234 ,round(IF ( P237= - 9999, NULL, P237),2) as
        value237 ,round(IF ( P240= - 9999, NULL, P240),2) as value240 ,round(IF ( P243= - 9999, NULL, P243),2) as
        value243 ,round(IF ( P246= - 9999, NULL, P246),2) as value246 ,round(IF ( P249= - 9999, NULL, P249),2) as
        value249 ,round(IF ( P252= - 9999, NULL, P252),2) as value252 ,round(IF ( P255= - 9999, NULL, P255),2) as
        value255 ,round(IF ( P258= - 9999, NULL, P258),2) as value258 ,round(IF ( P261= - 9999, NULL, P261),2) as
        value261 ,round(IF ( P264= - 9999, NULL, P264),2) as value264 ,round(IF ( P267= - 9999, NULL, P267),2) as
        value267 ,round(IF ( P270= - 9999, NULL, P270),2) as value270 ,round(IF ( P273= - 9999, NULL, P273),2) as
        value273 ,round(IF ( P276= - 9999, NULL, P276),2) as value276 ,round(IF ( P279= - 9999, NULL, P279),2) as
        value279 ,round(IF ( P282= - 9999, NULL, P282),2) as value282 ,round(IF ( P285= - 9999, NULL, P285),2) as
        value285,round(IF ( P288= - 9999, NULL, P288),2) as value288 FROM
        mos_plant_self_schedule where 1=1

        <if test="participantId != null and participantId != ''">
            and participant_id = #{participantId}
        </if>
        <if test="participantId == null or participantId == ''">
            <if test="groupId != null and groupId != ''">
                and participant_id in (select id from glbeco_participant where GROUP_ID = #{groupId})
            </if>
        </if>
        and data_time between #{startTime} and #{endTime}
        ) b on a.participant_id = b.participant_id and a.DATA_TIME = b.data_time
        ) a
    </select>
    <select id="dailySettlement" resultType="java.util.LinkedHashMap">
        SELECT
        (case data_type
        when '105' then '日前发电计划'
        when '106' then '中长期计划'
        when '108' then '日前省间出清'
        when '111' then '日前备用出清'
        when '206' then '实际结算电量'
        when '207' then '实时省间出清'
        when '211' then '酒钢开口合同'
        when '212' then '酒钢双边交易'
        else null end) as case_type,
        round(sum(value3),3) AS value3, round(sum(value6),3) AS value6, round(sum(value9),3) AS value9,
        round(sum(value12),3) AS value12, round(sum(value15),3) AS value15, round(sum(value18),3) AS value18,
        round(sum(value21),3) AS value21, round(sum(value24),3) AS value24, round(sum(value27),3) AS value27,
        round(sum(value30),3) AS value30, round(sum(value33),3) AS value33, round(sum(value36),3) AS value36,
        round(sum(value39),3) AS value39, round(sum(value42),3) AS value42, round(sum(value45),3) AS value45,
        round(sum(value48),3) AS value48, round(sum(value51),3) AS value51, round(sum(value54),3) AS value54,
        round(sum(value57),3) AS value57, round(sum(value60),3) AS value60, round(sum(value63),3) AS value63,
        round(sum(value66),3) AS value66, round(sum(value69),3) AS value69, round(sum(value72),3) AS value72,
        round(sum(value75),3) AS value75, round(sum(value78),3) AS value78, round(sum(value81),3) AS value81,
        round(sum(value84),3) AS value84, round(sum(value87),3) AS value87, round(sum(value90),3) AS value90,
        round(sum(value93),3) AS value93, round(sum(value96),3) AS value96, round(sum(value99),3) AS value99,
        round(sum(value102),3) AS value102, round(sum(value105),3) AS value105, round(sum(value108),3) AS value108,
        round(sum(value111),3) AS value111, round(sum(value114),3) AS value114, round(sum(value117),3) AS value117,
        round(sum(value120),3) AS value120, round(sum(value123),3) AS value123, round(sum(value126),3) AS value126,
        round(sum(value129),3) AS value129, round(sum(value132),3) AS value132, round(sum(value135),3) AS value135,
        round(sum(value138),3) AS value138, round(sum(value141),3) AS value141, round(sum(value144),3) AS value144,
        round(sum(value147),3) AS value147, round(sum(value150),3) AS value150, round(sum(value153),3) AS value153,
        round(sum(value156),3) AS value156, round(sum(value159),3) AS value159, round(sum(value162),3) AS value162,
        round(sum(value165),3) AS value165, round(sum(value168),3) AS value168, round(sum(value171),3) AS value171,
        round(sum(value174),3) AS value174, round(sum(value177),3) AS value177, round(sum(value180),3) AS value180,
        round(sum(value183),3) AS value183, round(sum(value186),3) AS value186, round(sum(value189),3) AS value189,
        round(sum(value192),3) AS value192, round(sum(value195),3) AS value195, round(sum(value198),3) AS value198,
        round(sum(value201),3) AS value201, round(sum(value204),3) AS value204, round(sum(value207),3) AS value207,
        round(sum(value210),3) AS value210, round(sum(value213),3) AS value213, round(sum(value216),3) AS value216,
        round(sum(value219),3) AS value219, round(sum(value222),3) AS value222, round(sum(value225),3) AS value225,
        round(sum(value228),3) AS value228, round(sum(value231),3) AS value231, round(sum(value234),3) AS value234,
        round(sum(value237),3) AS value237, round(sum(value240),3) AS value240, round(sum(value243),3) AS value243,
        round(sum(value246),3) AS value246, round(sum(value249),3) AS value249, round(sum(value252),3) AS value252,
        round(sum(value255),3) AS value255, round(sum(value258),3) AS value258, round(sum(value261),3) AS value261,
        round(sum(value264),3) AS value264, round(sum(value267),3) AS value267, round(sum(value270),3) AS value270,
        round(sum(value273),3) AS value273, round(sum(value276),3) AS value276, round(sum(value279),3) AS value279,
        round(sum(value282),3) AS value282, round(sum(value285),3) AS value285, round(sum(value288),3) AS value288
        FROM
        mos_settle_plant_base
        WHERE
        data_time between #{startTime} and #{endTime} and data_type in(105,106,108,111,206,207,211,212)
        <if test="participantId != null and participantId != ''">
            and participant_id = #{participantId}
        </if>
        <if test="participantId == null or participantId == ''">
            <if test="groupId != null and groupId != ''">
                and participant_id in (select id from glbeco_participant where GROUP_ID = #{groupId})
            </if>
        </if>
        group by data_type
        union all
        SELECT
        (case data_type
        when '101' then '日前正现货电量'
        when '102' then '日前负现货电量'
        when '103' then '日前正现货费用'
        when '104' then '日前负现货费用'
        when '201' then '实时正现货电量'
        when '202' then '实时负现货电量'
        when '203' then '实时正现货费用'
        when '204' then '实时负现货费用'
        when '557' then '中长期阻塞费用'
        when '558' then '阻塞风险对冲费用'
        else null end) as case_type,
        round(sum(value3),3) AS value3,
        round(sum(value6),3) AS value6,
        round(sum(value9),3) AS value9,
        round(sum(value12),3) AS value12,
        round(sum(value15),3) AS value15,
        round(sum(value18),3) AS value18,
        round(sum(value21),3) AS value21,
        round(sum(value24),3) AS value24,
        round(sum(value27),3) AS value27,
        round(sum(value30),3) AS value30,
        round(sum(value33),3) AS value33,
        round(sum(value36),3) AS value36,
        round(sum(value39),3) AS value39,
        round(sum(value42),3) AS value42,
        round(sum(value45),3) AS value45,
        round(sum(value48),3) AS value48,
        round(sum(value51),3) AS value51,
        round(sum(value54),3) AS value54,
        round(sum(value57),3) AS value57,
        round(sum(value60),3) AS value60,
        round(sum(value63),3) AS value63,
        round(sum(value66),3) AS value66,
        round(sum(value69),3) AS value69,
        round(sum(value72),3) AS value72,
        round(sum(value75),3) AS value75,
        round(sum(value78),3) AS value78,
        round(sum(value81),3) AS value81,
        round(sum(value84),3) AS value84,
        round(sum(value87),3) AS value87,
        round(sum(value90),3) AS value90,
        round(sum(value93),3) AS value93,
        round(sum(value96),3) AS value96,
        round(sum(value99),3) AS value99,
        round(sum(value102),3) AS value102,
        round(sum(value105),3) AS value105,
        round(sum(value108),3) AS value108,
        round(sum(value111),3) AS value111,
        round(sum(value114),3) AS value114,
        round(sum(value117),3) AS value117,
        round(sum(value120),3) AS value120,
        round(sum(value123),3) AS value123,
        round(sum(value126),3) AS value126,
        round(sum(value129),3) AS value129,
        round(sum(value132),3) AS value132,
        round(sum(value135),3) AS value135,
        round(sum(value138),3) AS value138,
        round(sum(value141),3) AS value141,
        round(sum(value144),3) AS value144,
        round(sum(value147),3) AS value147,
        round(sum(value150),3) AS value150,
        round(sum(value153),3) AS value153,
        round(sum(value156),3) AS value156,
        round(sum(value159),3) AS value159,
        round(sum(value162),3) AS value162,
        round(sum(value165),3) AS value165,
        round(sum(value168),3) AS value168,
        round(sum(value171),3) AS value171,
        round(sum(value174),3) AS value174,
        round(sum(value177),3) AS value177,
        round(sum(value180),3) AS value180,
        round(sum(value183),3) AS value183,
        round(sum(value186),3) AS value186,
        round(sum(value189),3) AS value189,
        round(sum(value192),3) AS value192,
        round(sum(value195),3) AS value195,
        round(sum(value198),3) AS value198,
        round(sum(value201),3) AS value201,
        round(sum(value204),3) AS value204,
        round(sum(value207),3) AS value207,
        round(sum(value210),3) AS value210,
        round(sum(value213),3) AS value213,
        round(sum(value216),3) AS value216,
        round(sum(value219),3) AS value219,
        round(sum(value222),3) AS value222,
        round(sum(value225),3) AS value225,
        round(sum(value228),3) AS value228,
        round(sum(value231),3) AS value231,
        round(sum(value234),3) AS value234,
        round(sum(value237),3) AS value237,
        round(sum(value240),3) AS value240,
        round(sum(value243),3) AS value243,
        round(sum(value246),3) AS value246,
        round(sum(value249),3) AS value249,
        round(sum(value252),3) AS value252,
        round(sum(value255),3) AS value255,
        round(sum(value258),3) AS value258,
        round(sum(value261),3) AS value261,
        round(sum(value264),3) AS value264,
        round(sum(value267),3) AS value267,
        round(sum(value270),3) AS value270,
        round(sum(value273),3) AS value273,
        round(sum(value276),3) AS value276,
        round(sum(value279),3) AS value279,
        round(sum(value282),3) AS value282,
        round(sum(value285),3) AS value285,
        round(sum(value288),3) AS value288
        FROM
        mos_settle_plant_day
        WHERE
        DATA_TIME between #{startTime} and #{endTime} and data_type in(101,102,103,104,201,202,203,204,557,558)
        <if test="participantId != null and participantId != ''">
            and participant_id = #{participantId}
        </if>
        <if test="participantId == null or participantId == ''">
            <if test="groupId != null and groupId != ''">
                and participant_id in (select id from glbeco_participant where GROUP_ID = #{groupId})
            </if>
        </if>
        group by data_type
    </select>
    <select id="selectOneTotal" resultType="java.lang.Integer">
        select settle_id
        from pmos_settle_info_total
        where data_time = #{dataTime}
          and element_id = #{elementId}
    </select>

    <insert id="insertTotal" useGeneratedKeys="true" keyProperty="settleId"
            parameterType="com.bobandata.cloud.trade.elec.dal.dataobject.settle.MlSettleTotalInfoDo">
        insert into pmos_settle_info_total (data_time, participant_id, settle_energy, reality_energy,
                                                  element_id, contract_energy, settle_fee, deviation_energy)
        values (#{dataTime}, #{participantId}, #{settleEnergy}, #{realityEnergy}, #{elementId},
                #{contractEnergy}, #{settleFee}, #{deviationEnergy});
    </insert>

    <update id="updateTotalById" useGeneratedKeys="true" keyProperty="settleId"
            parameterType="com.bobandata.cloud.trade.elec.dal.dataobject.settle.MlSettleTotalInfoDo">
        update pmos_settle_info_total
        set data_time=#{dataTime},
            participant_id=#{participantId},
            settle_energy=#{settleEnergy},
            reality_energy=#{realityEnergy},
            element_id=#{elementId},
            contract_energy=#{contractEnergy},
            settle_fee=#{settleFee},
            deviation_energy=#{deviationEnergy},
            case_type=#{caseType}
        where settle_id = #{settleId}
    </update>
</mapper>
