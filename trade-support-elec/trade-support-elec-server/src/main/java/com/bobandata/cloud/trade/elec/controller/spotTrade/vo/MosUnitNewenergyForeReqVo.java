package com.bobandata.cloud.trade.elec.controller.spotTrade.vo;

import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Schema(description = "新能源机组MOS预测数据 - 分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MosUnitNewenergyForeReqVo extends PageParam {

    @Schema(description = "物理单元ID", example = "UNIT_001")
    private String phyUnitId;

    @Schema(description = "曲线名称", example = "MOS预测曲线")
    private String curveName;

    @Schema(description = "曲线日期", example = "2024-07-30")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date curveDate;

    @Schema(description = "曲线类型", example = "2")
    private Integer curveType;

} 