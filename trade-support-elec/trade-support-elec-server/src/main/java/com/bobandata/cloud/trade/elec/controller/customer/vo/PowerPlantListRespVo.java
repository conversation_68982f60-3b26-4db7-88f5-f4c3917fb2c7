package com.bobandata.cloud.trade.elec.controller.customer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:24
 * @description
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@Schema(description = "厂站 Resp VO")
public class PowerPlantListRespVo {


    /**
     *交易单元id
     */
    @Schema(description = "element_id")
    private String elementId;

    /**
     * 在交易的participant_name
     */
    @Schema(description = "在交易的participant_name")
    private String participantName;

    /**
     * 发电单元code 也是sale_units_code
     */
    @Schema(description = "发电单元code 也是sale_units_code")
    private String unitCode;

    /**
     * 发电单元名称
     */
    @Schema(description = "发电单元名称")
    private String name;

    /**
     * 电厂类型
     */
    @Schema(description = "电厂类型")
    private String caseType;

    /**
     * 场站地址
     */
    @Schema(description = "场站地址")
    private String elementAddr;

    /**
     * 比例
     */
    @Schema(description = "比例")
    private Double ratio;

    /**
     * 装机容量
     */
    @Schema(description = "装机容量")
    private Double jyCap;


}
