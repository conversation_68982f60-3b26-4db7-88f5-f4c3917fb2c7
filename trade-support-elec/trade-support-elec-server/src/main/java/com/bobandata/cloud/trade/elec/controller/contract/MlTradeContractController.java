package com.bobandata.cloud.trade.elec.controller.contract;

import com.bobandata.cloud.trade.elec.controller.contract.vo.MlTradeContractListRespVo;
import com.bobandata.cloud.trade.elec.controller.contract.vo.MlTradeContractReqVo;
import com.bobandata.cloud.trade.elec.dal.dataobject.contract.MlTradeContract;
import com.bobandata.cloud.trade.elec.dal.mysql.contract.MlTradeContractMapper;
import com.bobandata.cloud.trade.elec.service.contract.MlTradeContractService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


@Tag(name = "交易中长期合同最终交易结果")
@Slf4j
@RequestMapping("/mlTradeContract")
@RestController
public class MlTradeContractController extends BaseCrudRestController<MlTradeContract, MlTradeContractMapper> {

    @Autowired
    private MlTradeContractService mlTradeContractService;


    @Operation(summary = "中长期list")
    @GetMapping("/list")
    public PagingResult<MlTradeContractListRespVo> listRecord(@Valid MlTradeContractReqVo reqVo) {
        return mlTradeContractService.listRecord(reqVo);
    }

}
