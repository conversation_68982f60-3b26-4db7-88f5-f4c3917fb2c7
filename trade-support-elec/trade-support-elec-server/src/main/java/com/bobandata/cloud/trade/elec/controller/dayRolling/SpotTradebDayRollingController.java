package com.bobandata.cloud.trade.elec.controller.dayRolling;

import com.bobandata.cloud.trade.elec.controller.dayRolling.vo.SpotTradeDayRollingListRespVo;
import com.bobandata.cloud.trade.elec.controller.dayRolling.vo.SpotTradeDayRollingReqVo;
import com.bobandata.cloud.trade.elec.dal.dataobject.dayRolling.SpotTradeDayRolling;
import com.bobandata.cloud.trade.elec.dal.mysql.dayRolling.SpotTradeDayRollingMapper;
import com.bobandata.cloud.trade.elec.service.dayRolling.SpotTradeDayRollingService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


@Tag(name = "交易中长期合同最终交易结果")
@Slf4j
@RequestMapping("/spotTradeDayRolling")
@RestController
public class SpotTradebDayRollingController extends BaseCrudRestController<SpotTradeDayRolling, SpotTradeDayRollingMapper> {

    @Autowired
    private SpotTradeDayRollingService spotTradeDayRollingService;


    @Operation(summary = "中长期list")
    @GetMapping("/list")
    public PagingResult<SpotTradeDayRollingListRespVo> listRecord(@Valid SpotTradeDayRollingReqVo reqVo) {
        return spotTradeDayRollingService.listRecord(reqVo);
    }

}
