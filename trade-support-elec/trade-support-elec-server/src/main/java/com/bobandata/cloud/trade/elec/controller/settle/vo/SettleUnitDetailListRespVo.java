package com.bobandata.cloud.trade.elec.controller.settle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Schema(description = "结算单元明细 - 列表 Response VO")
@Data
public class SettleUnitDetailListRespVo {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "单元编码")
    private String unitCode;

    @Schema(description = "数据时间")
    private String dataTime;

    @Schema(description = "结算属性")
    private String settleAttribute;

    @Schema(description = "电量")
    private BigDecimal energy;

    @Schema(description = "电价")
    private BigDecimal price;

    @Schema(description = "费用")
    private BigDecimal fee;

    @Schema(description = "创建时间")
    private Timestamp createTime; // 假设需要展示创建时间

} 