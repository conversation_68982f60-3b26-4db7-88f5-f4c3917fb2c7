package com.bobandata.cloud.trade.elec.controller.balance.vo;

import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:24
 * @description
 */
@Schema(description = "分页")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MlTradeBalanceReqVo extends PageParam {

    @Schema(description = "时间")
    protected String saleUnitsCode;



}
