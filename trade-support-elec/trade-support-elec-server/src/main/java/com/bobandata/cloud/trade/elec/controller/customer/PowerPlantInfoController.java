package com.bobandata.cloud.trade.elec.controller.customer;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.elec.controller.customer.vo.PowerPlantListRespVo;
import com.bobandata.cloud.trade.elec.controller.customer.vo.PowerPlantQueryRespVo;
import com.bobandata.cloud.trade.elec.controller.customer.vo.PowerPlantReqVo;
import com.bobandata.cloud.trade.elec.dal.dataobject.customer.PowerPlant;
import com.bobandata.cloud.trade.elec.dal.mysql.customer.PowerPlantMapper;
import com.bobandata.cloud.trade.elec.service.customer.PowerPlantService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static com.bobandata.cloud.common.pojo.ServiceResult.success;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:07
 * @description
 */
@Tag(name = "厂站档案")
@Slf4j
@RequestMapping("/powerPlant")
@RestController
public class PowerPlantInfoController extends BaseCrudRestController<PowerPlant, PowerPlantMapper> {

    @Autowired
    private PowerPlantService powerPlantService;

    @Operation(summary = "查询电厂信息")
    @GetMapping("/list")
    public PagingResult<PowerPlantListRespVo> listPowerPlant(@Valid PowerPlantReqVo reqVo) {
        return powerPlantService.listPowerPlant(reqVo);
    }

    @Operation(summary = "新增电厂信息")
    @PostMapping("/insert")
    public ServiceResult<Boolean> insertPowerPlant(@RequestBody PowerPlant powerPlant) {
        return powerPlantService.insert(powerPlant);
    }

    @Operation(summary = "修改电厂信息")
    @PostMapping("/update")
    public ServiceResult<Boolean> updatePowerPlant(@RequestBody PowerPlant powerPlant) {
        return powerPlantService.updatePowerPlant(powerPlant);
    }

    @Operation(summary = "删除电厂信息")
    @GetMapping("/delete")
    public ServiceResult<Boolean> deletePowerPlant(@RequestParam("id") String id) {
        return powerPlantService.delete(id);
    }


    @GetMapping("/getPlantType")
    @Operation(summary = "项目类型")
    public ServiceResult<List<Map>> getPlantType() {
        List<Map> list = powerPlantService.getPlantType();
        return success(list);
    }

    @GetMapping("/getFdjt")
    @Operation(summary = "发电集团")
    public ServiceResult<List<Map>> getFdjt() {
        List<Map> list = powerPlantService.getFdjt();
        return success(list);
    }

    //交易单元信息
    @GetMapping("/getParticipant")
    @Operation(summary = "厂站信息")
    public ServiceResult<List<PowerPlantQueryRespVo>> getParticipant(String name) {
        List<PowerPlantQueryRespVo> list = powerPlantService.getParticipant(name);
        return success(list);
    }
}
