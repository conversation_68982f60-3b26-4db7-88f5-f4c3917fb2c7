package com.bobandata.cloud.trade.elec.controller.customer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:24
 * @description
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@Schema(description = "厂站 Resp VO")
public class PowerPlantQueryRespVo {


    /**
     *交易单元id
     */
    @Schema(description = "id")
    private String id;

    /**
     * 发电单元名称
     */
    @Schema(description = "发电单元名称")
    private String name;


}
