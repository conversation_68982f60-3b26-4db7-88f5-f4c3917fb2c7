package com.bobandata.cloud.trade.elec.controller.spotTrade.vo;

import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Schema(description = "物理单元明细曲线 - 分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PhyDetailCurveReqVo extends PageParam {

    @Schema(description = "物理单元ID", example = "UNIT_002")
    private String phyUnitId;

    @Schema(description = "曲线名称", example = "实际出力曲线")
    private String curveName;

    @Schema(description = "曲线日期", example = "2024-07-30")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date curveDate;

    @Schema(description = "曲线类型", example = "ACTUAL_POWER")
    private String curveType;

} 