package com.bobandata.cloud.trade.elec.dal.mysql.dayRolling;


import com.bobandata.cloud.trade.elec.controller.dayRolling.vo.SpotTradeElementCurveListRespVo;
import com.bobandata.cloud.trade.elec.controller.dayRolling.vo.SpotTradeElementCurveReqVo;
import com.bobandata.cloud.trade.elec.dal.dataobject.dayRolling.SpotTradeElementCurve;
import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @createDate 2024-03-12 15:35:24
 * @Entity com.bobandata.cloud.trade.system.domain.MlTermContractEnergy
 */
public interface SpotTradeElementCurveMapper extends BaseCrudMapper<SpotTradeElementCurve> {

    default PagingResult<SpotTradeElementCurve> selectPage(SpotTradeElementCurveReqVo reqVO) {
        return selectPage(reqVO.toPagination(), new LambdaQueryWrapperX<SpotTradeElementCurve>()
//                .likeIfPresent(MlTradeContract::getPhyUnitName, reqVO.getPhyunitName())
//                .eqIfPresent(MlTradeContract::getType, reqVO.getPhyunitType())
//                .eqIfPresent(MlTradeContract::getElementId, reqVO.getElementName())
        );
    }

    List<Map<String, Object>> listRecord(SpotTradeElementCurveReqVo reqVo);

    List<SpotTradeElementCurveListRespVo> getCurve(SpotTradeElementCurveReqVo reqVo);

    List<Map<String, Object>> tradeReview(SpotTradeElementCurveReqVo reqVo);

    List<SpotTradeElementCurveListRespVo> getTradeReviewCurve(SpotTradeElementCurveReqVo reqVo);

    List<Map> getClearingData(SpotTradeElementCurveReqVo reqVo);
}




