package com.bobandata.cloud.trade.elec.controller.customer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:24
 * @description
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@Schema(description = "机组档案 Resp VO")
public class PhyunitListRespVo {

    /**
     * 机组ID
     */
    @Schema(description = "phy_unit_id")
    private String phyUnitId;

    /**
     * 机组名称
     */
    @Schema(description = "机组名称")
    private String phyUnitName;

    /**
     * 交易单元id
     */
    @Schema(description = "交易单元id")
    private String elementId;

    /**
     * 类型
     */
    @Schema(description = "类型")
    private Double type;

    /**
     * 正常容量
     */
    @Schema(description = "正常容量")
    private Double normalCap;

    /**
     * 最大容量
     */
    @Schema(description = "最大容量")
    private Double maxCap;

    /**
     * 最小容量
     */
    @Schema(description = "最小容量")
    private Double minCap;

    /**
     * 预测负载率
     */
    @Schema(description = "预测负载率")
    private Double foreLdratio;

    /**
     * 实际负载率
     */
    @Schema(description = "实际负载率")
    private Double realLdratio;


}
