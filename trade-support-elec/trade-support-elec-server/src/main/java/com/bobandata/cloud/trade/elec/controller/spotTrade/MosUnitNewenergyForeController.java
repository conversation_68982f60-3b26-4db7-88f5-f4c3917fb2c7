package com.bobandata.cloud.trade.elec.controller.spotTrade;

import com.bobandata.cloud.trade.elec.controller.spotTrade.vo.MosUnitNewenergyForeListRespVo;
import com.bobandata.cloud.trade.elec.controller.spotTrade.vo.MosUnitNewenergyForeReqVo;
import com.bobandata.cloud.trade.elec.service.spotTrade.MosUnitNewenergyForeService;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Tag(name = "现货交易 - 新能源机组MOS预测")
@Slf4j
@RequestMapping("/spotTrade/mosUnitNewenergyFore")
@RestController
@Validated
public class MosUnitNewenergyForeController {

    @Autowired
    private MosUnitNewenergyForeService mosUnitNewenergyForeService;

    @Operation(summary = "分页查询列表")
    @GetMapping("/list")
    public PagingResult<MosUnitNewenergyForeListRespVo> list(@Valid MosUnitNewenergyForeReqVo reqVo) {
        return mosUnitNewenergyForeService.list(reqVo);
    }


} 