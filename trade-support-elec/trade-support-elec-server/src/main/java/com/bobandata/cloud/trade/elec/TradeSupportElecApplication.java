package com.bobandata.cloud.trade.elec;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 * @date 2025-03-04日 14:30
 * @description
 */
@MapperScan(basePackages = {
        "com.bobandata.cloud.trade.*.dal.mysql"
})
@EnableFeignClients(basePackages = "com.bobandata")
@SpringBootApplication(scanBasePackages = "com.bobandata")
public class TradeSupportElecApplication {
    public static void main(String[] args) {
        SpringApplication.run(TradeSupportElecApplication.class, args);
    }
}
