package com.bobandata.cloud.trade.elec.controller.mlTerm.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.sql.Date;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:24
 * @description
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@Schema(description = "中长期记录详情 Resp VO")
public class MlTradeItemDetailListRespVo {



    /**
     * 交易时段
     */
    @Schema(description = "start_date")
    private Date startDate;

    /**
     * 交易所属时段 峰/平/谷
     */
    @Schema(description = "end_date")
    private Date endDate;


    @Schema(description = "time_division_code")
    private String timeDivisionCode;


    @Schema(description = "time_division_name")
    private String timeDivisionName;

    /**
     * 交易电量
     */
    @Schema(description = "trade_energy")
    private Double tradeEnergy;


    @Schema(description = "trade_price")
    private Double tradePrice;


    @Schema(description = "trade_fee")
    private Double tradeFee;


}
