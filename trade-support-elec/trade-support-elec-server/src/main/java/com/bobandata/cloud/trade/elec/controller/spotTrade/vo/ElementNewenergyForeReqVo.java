package com.bobandata.cloud.trade.elec.controller.spotTrade.vo;

import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Schema(description = "新能源元素预测数据 - 分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ElementNewenergyForeReqVo extends PageParam {

    @Schema(description = "元素ID", example = "ELEMENT_001")
    private String elementId;

    @Schema(description = "曲线名称", example = "预测出力曲线")
    private String curveName;

    @Schema(description = "曲线日期", example = "2024-07-30")
    @DateTimeFormat(pattern = "yyyy-MM-dd") // 指定日期格式，方便绑定
    private Date curveDate;

    @Schema(description = "曲线类型", example = "1")
    private Integer curveType;

} 