package com.bobandata.cloud.trade.elec.controller.settle;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.elec.controller.settle.vo.SettleReqParam;
import com.bobandata.cloud.trade.elec.dal.dataobject.settle.MlSettleTotalInfoDo;
import com.bobandata.cloud.trade.elec.dal.dataobject.settle.MlSettleTotaldetailDo;
import com.bobandata.cloud.trade.elec.dal.dataobject.settle.MlSettledetailDo;
import com.bobandata.cloud.trade.elec.dal.mysql.settle.MlSettleTotalInfoMapper;
import com.bobandata.cloud.trade.elec.service.settle.MlSettleTotalInfoService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;


@Tag(name = "结算管理-正式结算单查询")
@Slf4j
@RequestMapping("/settle")
@RestController
public class MlSettleTotalController extends BaseCrudRestController<MlSettleTotalInfoDo, MlSettleTotalInfoMapper> {

    @Autowired
    private MlSettleTotalInfoService totalInfoService;

    @Operation(summary = "获得每月结算单")
    @Parameter(name = "month", description = "月份", example = "yyyy-MM格式")
    @GetMapping("/total")
    public PagingResult<MlSettleTotalInfoDo> listSettleTotal(SettleReqParam settleReqParam) {
        PagingResult<MlSettleTotalInfoDo> mlSettleTotalInfoDos = totalInfoService.listByMonth(settleReqParam);
        return mlSettleTotalInfoDos;
    }

    @Operation(summary = "获得当前结算单汇总")
    @Parameter(name = "settleId", description = "结算单id", example = "")
    @GetMapping("/info")
    public ServiceResult<List<MlSettleTotaldetailDo>> listSettleInfo(@RequestParam(value = "settleId") String settleId) {
        return ServiceResult.success(totalInfoService.listSettleInfo(Integer.valueOf(settleId)));
    }


    @Operation(summary = "获得当前结算单明细")
    @Parameter(name = "settleId", description = "结算单id", example = "")
    @GetMapping("/detail")
    public PagingResult<MlSettledetailDo> listSettleDetail(SettleReqParam settleReqParam) {
        PagingResult<MlSettledetailDo> mlSettleDetails = totalInfoService.listSettleDetail(settleReqParam);
        return mlSettleDetails;
    }

    //现货管理日出清
    @Operation(summary = "现货管理日出清")
    @GetMapping("/dailyClearing")
    public ServiceResult<List<LinkedHashMap>> dailyClearing(SettleReqParam settleReqParam) {
        List<LinkedHashMap> data = totalInfoService.dailyClearing(settleReqParam);
        return ServiceResult.success(data);
    }

    @Operation(summary = "现货管理日结算")
    @GetMapping("/dailySettlement")
    public ServiceResult<List<LinkedHashMap>> dailySettlement(SettleReqParam settleReqParam) {
        List<LinkedHashMap> data = totalInfoService.dailySettlement(settleReqParam);
        return ServiceResult.success(data);
    }


    //厂站查询条件
    @Operation(summary = "厂站查询条件")
    @GetMapping("/getParticipant")
    public ServiceResult<List<HashMap>> getParticipant(SettleReqParam settleReqParam) {
        List<HashMap> participant = totalInfoService.getParticipant(settleReqParam);
        return ServiceResult.success(participant);
    }

}
