package com.bobandata.cloud.trade.elec.controller.settle.vo;

import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@Schema(description = "用户端项目 Request VO")
public class SettleReqParam extends PageParam {

    @Schema(description = "项目名称", example = "XXXXX")
    private String month;


    @Schema(required = false, description = "0 查询申请中，1 查询已加入", example = "XXXXX")
    private String participantId;

    @Schema(required = false, description = "0 查询申请中，1 查询已加入", example = "XXXXX")
    private String groupId;

    @Schema(required = false, description = "0 查询申请中，1 查询已加入", example = "XXXXX")
    private String settleId;

    @Schema(required = false, description = "0 查询申请中，1 查询已加入", example = "XXXXX")
    private String startTime;

    @Schema(required = false, description = "0 查询申请中，1 查询已加入", example = "XXXXX")
    private String endTime;


}
