package com.bobandata.cloud.trade.elec.controller.spotTrade.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Schema(description = "物理单元明细曲线 - 列表 Response VO")
@Data
public class PhyDetailCurveListRespVo {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "物理单元ID")
    private String phyUnitId;

    @Schema(description = "曲线名称")
    private String curveName;

    @Schema(description = "曲线日期")
    private Date curveDate;

    @Schema(description = "曲线类型")
    private String curveType;

    // P1-P96 字段，默认不暴露，按需添加或封装

} 