package com.bobandata.cloud.trade.elec.controller.excelImport;

import cn.hutool.core.io.FileTypeUtil;
import com.bobandata.cloud.common.pojo.ServiceResult;

import com.bobandata.cloud.trade.elec.controller.util.FileConversionUtil;
import com.bobandata.cloud.trade.elec.excel.processor.FileProcessor;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Description;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.bobandata.cloud.common.pojo.ServiceResult.success;

/**
 * <AUTHOR>
 * @Date 2024/8/14 15:08
 * @Classname ImportExcelController
 * @Description
 */
@Slf4j
@RequestMapping("/excel")
@RestController
public class ImportExcelController {

    @Autowired
    private FileProcessor fileProcessor;

    @Operation(summary = "上传表格文件")
    @PostMapping(value = "/upload")
    @ResponseBody
    public ServiceResult uploadFile(@RequestParam(value = "file") MultipartFile[] files) {
        if (files == null || files.length == 0) {
            return ServiceResult.error(10003, "没有文件被上传");
        }
        String dirPath = null;
        File formatFile = null;
        // 首先验证所有文件
        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                continue;
            }
            try (InputStream inputStream = file.getInputStream()) {
                String type = FileTypeUtil.getType(inputStream, file.getOriginalFilename());
                if (!"xlsx".equals(type) && !"xls".equals(type) && !"csv".equals(type)) {
                    return ServiceResult.error(10003, "文件格式只支持Excel");
                }
                long size = file.getSize();
                if (FileConversionUtil.checkFileSize(size, 5, "M")) {
                    return ServiceResult.error(10003, "文件大小限制为5M");
                }
            } catch (IOException e) {
                log.error("文件格式校验失败", e);
                return ServiceResult.error(1, "文件格式校验失败");
            }
        }

        // 然后处理转换
        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                continue;
            }
            log.info("正在上传的文件：{}", file.getOriginalFilename());
            try {
                // 确保每次迭代都释放前一个文件资源
                formatFile = FileConversionUtil.convertMultipartFileToFile(file);
                if (dirPath == null) {
                    dirPath = formatFile.getParent();
                }
            } catch (Exception e) {
                log.error("文件上传失败", e);
                return ServiceResult.error(1, "文件上传失败");
            }
        }
        return success(dirPath);
    }

    @PostMapping("/submit")
    public ServiceResult handleFolderUpload(@RequestParam("folderAddress") String folderAddress) {
        // 参数校验
        if (folderAddress == null || folderAddress.trim().isEmpty()) {
            return ServiceResult.error(1003, "文件夹地址为空");
        }
        // 指定要读取的文件夹路径
        Path folderPath = Paths.get(folderAddress.replace("/", File.separator));
        if (!Files.exists(folderPath)) {
            return ServiceResult.error(1003, "文件夹路径无效");
        }
        try {
            List<File> files = Files.walk(folderPath)
                    .filter(Files::isRegularFile)
                    .filter(path -> ".xlsx,.xls,.csv".contains(path.toString().toLowerCase().substring(path.toString().lastIndexOf('.') + 1)))
                    .map(Path::toFile)
                    .collect(Collectors.toList());

            if (files.isEmpty()) {
                return ServiceResult.error(1003, "在文件夹中找不到Excel文件");
            }
            // 异步处理文件以提高响应性
            ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
            List<Future<?>> futures = new ArrayList<>();
            for (File file : files) {
                futures.add(executorService.submit(new Runnable() {
                    @Override
                    public void run() {
                        processFile(file);
                    }
                }));
            }
// 等待所有任务完成
            for (Future<?> future : futures) {
                future.get();
            }
            // 关闭ExecutorService
            executorService.shutdown();
        } catch (IOException e) {
            log.error("文件读取失败", e);
            return ServiceResult.error(1, "文件读取失败");
        } catch (Exception e) {
            log.error("提交失败", e);
            return ServiceResult.error(1, "提交失败");
        }
        return ServiceResult.success();
    }

    private void processFile(File file) {
        try {
            fileProcessor.dealImportMethod(file);
        } catch (Exception e) {
            log.error("处理文件时出错: " + file.getName(), e);
        } finally {
            if (!file.delete()) {
                log.warn("删除文件时出错: " + file.getName());
            }
        }
    }

    @PostMapping("/delTempFolder")
    public ServiceResult delTempFolder(@RequestParam("folderAddress") String folderAddress) {
        // 参数校验
        if (folderAddress == null || folderAddress.trim().isEmpty()) {
            return ServiceResult.error(1003, "Folder address is empty");
        }
        File directory = new File(folderAddress);
        if (directory.exists() && directory.isDirectory()) {
            deleteRecursively(directory);
            return ServiceResult.success("Deleted successfully");
            // return ServiceResult.error(1003, "Failed to delete");
        }
        return ServiceResult.error(1003, "Directory does not exist");
    }

    /**
     * 递归删除指定的文件或文件夹。
     *
     * @param file 要删除的文件或文件夹
     * @return 删除是否成功
     */
    public void deleteRecursively(File file) {
        // 如果是文件夹，则先递归删除其中的所有文件和子文件夹
        File[] files = file.listFiles();
        if (files != null) {
            for (File f : files) {
                if (file.isDirectory()) {
                    deleteRecursively(f); // 递归删除子目录
                } else {
                    f.delete(); // 删除文件
                }
            }
        }
    }

    @Description("仅本地使用提交文件夹")
    @PostMapping("/submit/folder")
    public ServiceResult submitFolderUpload(@RequestParam("folderAddress") String folderAddress) {
        // 参数校验
        if (folderAddress == null || folderAddress.trim().isEmpty()) {
            return ServiceResult.error(1003, "Folder address is empty");
        }
        // 指定要读取的文件夹路径
        Path folderPath = Paths.get(folderAddress);
        if (!Files.exists(folderPath)) {
            return ServiceResult.error(1003, "Invalid folder path");
        }
        try {
            List<File> files = Files.walk(folderPath)
                    .filter(Files::isRegularFile)
                    .filter(path -> ".xlsx,.xls".contains(path.toString().toLowerCase().substring(path.toString().lastIndexOf('.') + 1)))
                    .map(Path::toFile)
                    .collect(Collectors.toList());

            if (files.isEmpty()) {
                return ServiceResult.error(1003, "No Excel files found in the folder");
            }
            // 异步处理文件以提高响应性
            ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
            files.forEach(file -> executorService.submit(() -> fileProcessor.dealImportMethod(file)));
            executorService.shutdown();
        } catch (IOException e) {
            log.error("文件读取失败", e);
            return ServiceResult.error(1, "文件读取失败");
        } catch (Exception e) {
            log.error("导入失败", e);
            return ServiceResult.error(1, "导入失败");
        }
        return ServiceResult.success();
    }
}
