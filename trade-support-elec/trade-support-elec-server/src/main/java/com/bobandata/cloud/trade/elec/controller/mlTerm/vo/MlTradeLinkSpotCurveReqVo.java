package com.bobandata.cloud.trade.elec.controller.mlTerm.vo;

import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.sql.Date;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:24
 * @description
 */
@Schema(description = "分页")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MlTradeLinkSpotCurveReqVo extends PageParam {

    @Schema(description = "曲线类型")
    protected String curveType;

    @Schema(description = "发电侧/售电侧的unitCode 交易单元的code")
    protected String UnitCode;

    @Schema(description = "曲线时间")
    protected Date curveDate;

    @Schema(description = "曲线时间")
    protected String startTime;

    @Schema(description = "曲线时间")
    protected String endTime;

    @Schema(description = "数据类型")
    protected String dataType;



}
