package com.bobandata.cloud.trade.elec.controller.mlTerm;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.elec.controller.mlTerm.vo.MlTradeHoldCurveListRespVo;
import com.bobandata.cloud.trade.elec.controller.mlTerm.vo.MlTradeHoldCurveReqVo;
import com.bobandata.cloud.trade.elec.dal.dataobject.mlTerm.MlTradeHoldCurve;
import com.bobandata.cloud.trade.elec.dal.mysql.mlTerm.MlTradeHoldCurveMapper;
import com.bobandata.cloud.trade.elec.service.mlTerm.MlTradeHoldCurveService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;


@Tag(name = "中长期数据查询")
@Slf4j
@RequestMapping("/mlTradeHoldCurve")
@RestController
public class MlTradeHoldCurveController extends BaseCrudRestController<MlTradeHoldCurve, MlTradeHoldCurveMapper> {

    @Autowired
    private MlTradeHoldCurveService mlTradeHoldCurveService;

    @Operation(summary = "汇总数据")
    @GetMapping("/list")
    public ServiceResult<List<Map<String, Object>>> list(@Valid MlTradeHoldCurveReqVo reqVo) {
        return mlTradeHoldCurveService.list(reqVo);
    }


    @Operation(summary = "96点数据")
    @GetMapping("/getCurve")
    public ServiceResult<List<MlTradeHoldCurveListRespVo>> getCurve(@Valid MlTradeHoldCurveReqVo reqVo) {
        return mlTradeHoldCurveService.getCurve(reqVo);
    }

    @Operation(summary = "市场发布信息96点数据")
    @GetMapping("/getMarketReleaseCurve")
    public ServiceResult<List<MlTradeHoldCurveListRespVo>> getMarketReleaseCurve(@Valid MlTradeHoldCurveReqVo reqVo) {
        return mlTradeHoldCurveService.getMarketReleaseCurve(reqVo);
    }
}
