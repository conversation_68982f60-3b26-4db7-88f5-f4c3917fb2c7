package com.bobandata.cloud.trade.elec.controller.mlTerm;

import com.bobandata.cloud.trade.elec.controller.mlTerm.vo.MlTradeItemDetailListRespVo;
import com.bobandata.cloud.trade.elec.controller.mlTerm.vo.MlTradeItemDetailReqVo;
import com.bobandata.cloud.trade.elec.dal.dataobject.mlTerm.MlTradeItemDetail;
import com.bobandata.cloud.trade.elec.dal.mysql.mlTerm.MlTradeItemDetailMapper;
import com.bobandata.cloud.trade.elec.service.mlTerm.MlTradeItemDetailService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


@Tag(name = "中长期数据查询")
@Slf4j
@RequestMapping("/mlTradeItemDetail")
@RestController
public class MlTradeItemDetailController extends BaseCrudRestController<MlTradeItemDetail, MlTradeItemDetailMapper> {

    @Autowired
    private MlTradeItemDetailService mlTradeItemDetailService;


    @Operation(summary = "中长期数据详情")
    @GetMapping("/list")
    public PagingResult<MlTradeItemDetailListRespVo> listDetail(@Valid MlTradeItemDetailReqVo reqVo) {
        return mlTradeItemDetailService.listDetail(reqVo);
    }
}
