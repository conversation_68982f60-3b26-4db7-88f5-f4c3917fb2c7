package com.bobandata.cloud.trade.elec.controller.customer;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.elec.controller.customer.vo.PhyunitListRespVo;
import com.bobandata.cloud.trade.elec.controller.customer.vo.PhyunitReqVo;
import com.bobandata.cloud.trade.elec.dal.dataobject.customer.Phyunit;
import com.bobandata.cloud.trade.elec.dal.mysql.customer.PhyunitMapper;
import com.bobandata.cloud.trade.elec.service.customer.PhyunitService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static com.bobandata.cloud.common.pojo.ServiceResult.success;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:07
 * @description
 */
@Tag(name = "机组档案")
@Slf4j
@RequestMapping("/phyunit")
@RestController
public class PhyunitController extends BaseCrudRestController<Phyunit, PhyunitMapper> {

    @Autowired
    private PhyunitService phyunitService;

    @Operation(summary = "查询机组信息")
    @GetMapping("/list")
    public PagingResult<PhyunitListRespVo> listPowerPlant(@Valid PhyunitReqVo reqVo) {
        return phyunitService.listPowerPlant(reqVo);
    }

    @Operation(summary = "新增机组信息")
    @PostMapping("/insert")
    public ServiceResult<Boolean> insertPowerPlant(@RequestBody Phyunit powerPlant) {
        return phyunitService.insert(powerPlant);
    }

    @Operation(summary = "修改机组信息")
    @PostMapping("/update")
    public ServiceResult<Boolean> updatePowerPlant(@RequestBody Phyunit powerPlant) {
        return phyunitService.updatePowerPlant(powerPlant);
    }

    @Operation(summary = "删除机组信息")
    @GetMapping("/delete")
    public ServiceResult<Boolean> deletePowerPlant(@RequestParam("id") String id) {
        return phyunitService.delete(id);
    }



    @GetMapping("/getPlantType")
    @Operation(summary = "项目类型")
    public ServiceResult<List<Map>> getPlantType() {
        List<Map> list = phyunitService.getPlantType();
        return success(list);
    }

    @GetMapping("/getJydy")
    @Operation(summary = "交易单元")
    public ServiceResult<List<Map>> getJydy() {
        List<Map> list = phyunitService.getJydy();
        return success(list);
    }
}
