package com.bobandata.cloud.trade.elec.controller.mlTerm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:24
 * @description
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@Schema(description = "中长期记录详情 Resp VO")
public class MlTradeHoldCurveListRespVo {


    @Schema(description = "曲线类型")
    protected String curveType;

    @Schema(description = "p1")
    private Double p1;
    @Schema(description = "p2")
    private Double p2;
    @Schema(description = "p3")
    private Double p3;
    @Schema(description = "p4")
    private Double p4;
    @Schema(description = "p5")
    private Double p5;
    @Schema(description = "p6")
    private Double p6;
    @Schema(description = "p7")
    private Double p7;
    @Schema(description = "p8")
    private Double p8;
    @Schema(description = "p9")
    private Double p9;
    @Schema(description = "p10")
    private Double p10;
    @Schema(description = "p11")
    private Double p11;
    @Schema(description = "p12")
    private Double p12;
    @Schema(description = "p13")
    private Double p13;
    @Schema(description = "p14")
    private Double p14;
    @Schema(description = "p15")
    private Double p15;
    @Schema(description = "p16")
    private Double p16;
    @Schema(description = "p17")
    private Double p17;
    @Schema(description = "p18")
    private Double p18;
    @Schema(description = "p19")
    private Double p19;
    @Schema(description = "p20")
    private Double p20;
    @Schema(description = "p21")
    private Double p21;
    @Schema(description = "p22")
    private Double p22;
    @Schema(description = "p23")
    private Double p23;
    @Schema(description = "p24")
    private Double p24;
    @Schema(description = "p25")
    private Double p25;
    @Schema(description = "p26")
    private Double p26;
    @Schema(description = "p27")
    private Double p27;
    @Schema(description = "p28")
    private Double p28;
    @Schema(description = "p29")
    private Double p29;
    @Schema(description = "p30")
    private Double p30;
    @Schema(description = "p31")
    private Double p31;
    @Schema(description = "p32")
    private Double p32;
    @Schema(description = "p33")
    private Double p33;
    @Schema(description = "p34")
    private Double p34;
    @Schema(description = "p35")
    private Double p35;
    @Schema(description = "p36")
    private Double p36;
    @Schema(description = "p37")
    private Double p37;
    @Schema(description = "p38")
    private Double p38;
    @Schema(description = "p39")
    private Double p39;
    @Schema(description = "p40")
    private Double p40;
    @Schema(description = "p41")
    private Double p41;
    @Schema(description = "p42")
    private Double p42;
    @Schema(description = "p43")
    private Double p43;
    @Schema(description = "p44")
    private Double p44;
    @Schema(description = "p45")
    private Double p45;
    @Schema(description = "p46")
    private Double p46;
    @Schema(description = "p47")
    private Double p47;
    @Schema(description = "p48")
    private Double p48;
    @Schema(description = "p49")
    private Double p49;
    @Schema(description = "p50")
    private Double p50;
    @Schema(description = "p51")
    private Double p51;
    @Schema(description = "p52")
    private Double p52;
    @Schema(description = "p53")
    private Double p53;
    @Schema(description = "p54")
    private Double p54;
    @Schema(description = "p55")
    private Double p55;
    @Schema(description = "p56")
    private Double p56;
    @Schema(description = "p57")
    private Double p57;
    @Schema(description = "p58")
    private Double p58;
    @Schema(description = "p59")
    private Double p59;
    @Schema(description = "p60")
    private Double p60;
    @Schema(description = "p61")
    private Double p61;
    @Schema(description = "p62")
    private Double p62;
    @Schema(description = "p63")
    private Double p63;
    @Schema(description = "p64")
    private Double p64;
    @Schema(description = "p65")
    private Double p65;
    @Schema(description = "p66")
    private Double p66;
    @Schema(description = "p67")
    private Double p67;
    @Schema(description = "p68")
    private Double p68;
    @Schema(description = "p69")
    private Double p69;
    @Schema(description = "p70")
    private Double p70;
    @Schema(description = "p71")
    private Double p71;
    @Schema(description = "p72")
    private Double p72;
    @Schema(description = "p73")
    private Double p73;
    @Schema(description = "p74")
    private Double p74;
    @Schema(description = "p75")
    private Double p75;
    @Schema(description = "p76")
    private Double p76;
    @Schema(description = "p77")
    private Double p77;
    @Schema(description = "p78")
    private Double p78;
    @Schema(description = "p79")
    private Double p79;
    @Schema(description = "p80")
    private Double p80;
    @Schema(description = "p81")
    private Double p81;
    @Schema(description = "p82")
    private Double p82;
    @Schema(description = "p83")
    private Double p83;
    @Schema(description = "p84")
    private Double p84;
    @Schema(description = "p85")
    private Double p85;
    @Schema(description = "p86")
    private Double p86;
    @Schema(description = "p87")
    private Double p87;
    @Schema(description = "p88")
    private Double p88;
    @Schema(description = "p89")
    private Double p89;
    @Schema(description = "p90")
    private Double p90;
    @Schema(description = "p91")
    private Double p91;
    @Schema(description = "p92")
    private Double p92;
    @Schema(description = "p93")
    private Double p93;
    @Schema(description = "p94")
    private Double p94;
    @Schema(description = "p95")
    private Double p95;
    @Schema(description = "p96")
    private Double p96;



}
