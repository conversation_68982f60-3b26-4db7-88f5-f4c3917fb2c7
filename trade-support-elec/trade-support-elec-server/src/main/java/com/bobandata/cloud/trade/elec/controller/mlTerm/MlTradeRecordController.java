package com.bobandata.cloud.trade.elec.controller.mlTerm;

import com.bobandata.cloud.trade.elec.controller.mlTerm.vo.MlTradeRecordListRespVo;
import com.bobandata.cloud.trade.elec.controller.mlTerm.vo.MlTradeRecordReqVo;
import com.bobandata.cloud.trade.elec.dal.dataobject.mlTerm.MlTradeRecord;
import com.bobandata.cloud.trade.elec.dal.mysql.mlTerm.MlTradeRecordMapper;
import com.bobandata.cloud.trade.elec.service.mlTerm.MlTradeRecordService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


@Tag(name = "中长期数据查询")
@Slf4j
@RequestMapping("/mlTradeRecord")
@RestController
public class MlTradeRecordController extends BaseCrudRestController<MlTradeRecord, MlTradeRecordMapper> {

    @Autowired
    private MlTradeRecordService mlTradeRecordService;


    @Operation(summary = "中长期list")
    @GetMapping("/list")
    public PagingResult<MlTradeRecordListRespVo> listRecord(@Valid MlTradeRecordReqVo reqVo) {
        return mlTradeRecordService.listRecord(reqVo);
    }

}
