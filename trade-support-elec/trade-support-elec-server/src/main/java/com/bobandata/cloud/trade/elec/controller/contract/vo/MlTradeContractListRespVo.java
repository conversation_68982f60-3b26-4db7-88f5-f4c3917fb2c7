package com.bobandata.cloud.trade.elec.controller.contract.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.sql.Date;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:24
 * @description
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@Schema(description = "中长期记录 Resp VO")
public class MlTradeContractListRespVo {

    /**
     * 所属交易批次ID
     */
    @Schema(description = "User name")
    private String userName;

    @Schema(description = "Unit name")
    private String unitName;

    @Schema(description = "Unit ID")
    private String unitId;

    @Schema(description = "Trade type")
    private String tradeType;

    @Schema(description = "GUID")
    private String guid;

    @Schema(description = "Time division range")
    private String timeDivisionRange;

    @Schema(description = "Time division name")
    private String timeDivisionName;

    @Schema(description = "Time division code")
    private String timeDivisionCode;

    @Schema(description = "Trade sequence ID")
    private String tradeseqId;

    @Schema(description = "Interval index")
    private Integer intervalIndex;

    @Schema(description = "Sale units code")
    private String saleUnitsCode;

    @Schema(description = "Sale units name")
    private String saleUnitsName;

    @Schema(description = "Vendee units code")
    private String vendeeUnitsCode;

    @Schema(description = "Vendee units name")
    private String vendeeUnitsName;

    @Schema(description = "Vendee participant name")
    private String vendeeParticipantname;

    @Schema(description = "Sale participant name")
    private String saleParticipantname;

    @Schema(description = "Sale participant ID")
    private String saleParticipantId;

    @Schema(description = "Vendee participant ID")
    private String vendeeParticipantId;

    @Schema(description = "Vendee energy")
    private Double vendeeEnergy;

    @Schema(description = "Sale energy")
    private Double saleEnergy;

    @Schema(description = "Vendee price")
    private Double vendeePrice;

    @Schema(description = "Sale price")
    private Double salePrice;

    @Schema(description = "Transaction begin time")
    private Date trBeginTime;

    @Schema(description = "Transaction end time")
    private Date trEndTime;

    @Schema(description = "Start time")
    private Date startTime;

    @Schema(description = "End time")
    private Date endTime;

    @Schema(description = "Trade caption")
    private String tradeCaption;

    @Schema(description = "Time part name")
    private String timePartName;

    @Schema(description = "Time part content")
    private String timePartContent;

    @Schema(description = "Vendee energy sum")
    private Double vendeeEnergySum;

    @Schema(description = "Sale energy sum")
    private Double saleEnergySum;

}
