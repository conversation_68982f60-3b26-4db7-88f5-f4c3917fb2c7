package com.bobandata.cloud.trade.elec.controller.mlTerm;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.elec.controller.mlTerm.vo.MlTradeLinkSpotCurveListRespVo;
import com.bobandata.cloud.trade.elec.controller.mlTerm.vo.MlTradeLinkSpotCurveReqVo;
import com.bobandata.cloud.trade.elec.dal.dataobject.mlTerm.MlTradeLinkSpotCurve;
import com.bobandata.cloud.trade.elec.dal.mysql.mlTerm.MlTradeLinkSpotCurveMapper;
import com.bobandata.cloud.trade.elec.service.mlTerm.MlTradeLinkSpotCurveService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;


@Tag(name = "中长期数据查询")
@Slf4j
@RequestMapping("/mlTradeLinkSpotCurve")
@RestController
public class MlTradeLinkSpotCurveController extends BaseCrudRestController<MlTradeLinkSpotCurve, MlTradeLinkSpotCurveMapper> {

    @Autowired
    private MlTradeLinkSpotCurveService mlTradeLinkSpotCurveService;


    @Operation(summary = "汇总数据")
    @GetMapping("/list")
    public ServiceResult<List<Map<String, Object>>> list(@Valid MlTradeLinkSpotCurveReqVo reqVo) {
        return mlTradeLinkSpotCurveService.list(reqVo);
    }


    @Operation(summary = "96点数据")
    @GetMapping("/getCurve")
    public ServiceResult<List<MlTradeLinkSpotCurveListRespVo>> getCurve(@Valid MlTradeLinkSpotCurveReqVo reqVo) {
        return mlTradeLinkSpotCurveService.getCurve(reqVo);
    }
}
