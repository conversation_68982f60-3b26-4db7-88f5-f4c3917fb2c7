package com.bobandata.cloud.trade.elec.controller.balance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.sql.Date;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:24
 * @description
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@Schema(description = "中长期记录 Resp VO")
public class MlTradeBalanceListRespVo {

    @Schema(description = "user_name")
    private String userName;

    @Schema(description = "unit_name")
    private String unitName;

    @Schema(description = "unit_id")
    private String unitId;

    @Schema(description = "trade_type")
    private String tradeType;

    @Schema(description = "sale_units_code")
    private String saleUnitsCode;

    @Schema(description = "sale_units_name")
    private String saleUnitsName;

    @Schema(description = "guid")
    private String guid;

    @Schema(description = "trade_caption")
    private String tradeCaption;

    @Schema(description = "time_division_range")
    private String timeDivisionRange;

    @Schema(description = "time_division_name")
    private String timeDivisionName;

    @Schema(description = "time_division_code")
    private String timeDivisionCode;

    @Schema(description = "tradeseq_id")
    private String tradeseqId;

    @Schema(description = "se_route_id")
    private String seRouteId;

    @Schema(description = "interval_index")
    private Integer intervalIndex;

    @Schema(description = "trade_time_part")
    private Integer tradeTimePart;

    @Schema(description = "band_no")
    private Integer bandNo;


    @Schema(description = "vendee_units_code")
    private String vendeeUnitsCode;


    @Schema(description = "vendee_units_name")
    private String vendeeUnitsName;

    @Schema(description = "sale_participant_id")
    private String saleParticipantId;

    @Schema(description = "sale_participant_name")
    private String saleParticipantName;

    @Schema(description = "vendee_participant_id")
    private String vendeeParticipantId;

    @Schema(description = "vendee_participant_name")
    private String vendeeParticipantName;

    @Schema(description = "vendee_energy")
    private Double vendeeEnergy;

    @Schema(description = "sale_energy")
    private Double saleEnergy;

    @Schema(description = "vendee_price")
    private Double vendeePrice;

    @Schema(description = "sale_price")
    private Double salePrice;

    @Schema(description = "start_time")
    private Date startTime;

    @Schema(description = "end_time")
    private Date endTime;


    @Schema(description = "clearing_energy")
    private Double clearingEnergy;

    @Schema(description = "clearing_price")
    private Double clearingPrice;

    /**
     * 创建人
     */
    @Schema(description = "creatorId")
    private Long creatorId;

    /**
     * 最后修改人
     */
    @Schema(description = "lastModifierId")
    private Long lastModifierId;

    /**
     *刷新时间
     */
    @Schema(description = "lastRefreshTime")
    private Timestamp lastRefreshTime;
}
