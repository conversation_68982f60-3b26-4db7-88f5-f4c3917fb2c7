package com.bobandata.cloud.trade.elec.controller.spotTrade;

import com.bobandata.cloud.trade.elec.controller.spotTrade.vo.SharedMosDayCurveListRespVo;
import com.bobandata.cloud.trade.elec.controller.spotTrade.vo.SharedMosDayCurveReqVo;
import com.bobandata.cloud.trade.elec.service.spotTrade.SharedMosDayCurveService;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Tag(name = "现货交易 - 共享调峰日曲线")
@Slf4j
@RequestMapping("/spotTrade/sharedMosDayCurve")
@RestController
@Validated
public class SharedMosDayCurveController {

    @Autowired
    private SharedMosDayCurveService sharedMosDayCurveService;

    @Operation(summary = "分页查询列表")
    @GetMapping("/list")
    public PagingResult<SharedMosDayCurveListRespVo> list(@Valid SharedMosDayCurveReqVo reqVo) {
        return sharedMosDayCurveService.list(reqVo);
    }

} 