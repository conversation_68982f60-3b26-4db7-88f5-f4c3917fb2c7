package com.bobandata.cloud.trade.elec.controller.dayRolling;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.elec.controller.dayRolling.vo.SpotTradeElementCurveListRespVo;
import com.bobandata.cloud.trade.elec.controller.dayRolling.vo.SpotTradeElementCurveReqVo;
import com.bobandata.cloud.trade.elec.dal.dataobject.dayRolling.SpotTradeElementCurve;
import com.bobandata.cloud.trade.elec.dal.mysql.dayRolling.SpotTradeElementCurveMapper;
import com.bobandata.cloud.trade.elec.service.dayRolling.SpotTradeElementCurveService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;


@Tag(name = "现货曲线")
@Slf4j
@RequestMapping("/spotTradeElementCurve")
@RestController
public class SpotTradeElementCurveController extends BaseCrudRestController<SpotTradeElementCurve, SpotTradeElementCurveMapper> {

    @Autowired
    private SpotTradeElementCurveService spotTradeElementCurveService;


    @Operation(summary = "现货曲线")
    @GetMapping("/list")
    public ServiceResult<List<Map<String, Object>>> listRecord(@Valid SpotTradeElementCurveReqVo reqVo) {
        return spotTradeElementCurveService.listRecord(reqVo);
    }

    @Operation(summary = "96点数据")
    @GetMapping("/getCurve")
    public ServiceResult<List<SpotTradeElementCurveListRespVo>> getCurve(@Valid SpotTradeElementCurveReqVo reqVo) {
        return spotTradeElementCurveService.getCurve(reqVo);
    }

    //交易复盘
    @Operation(summary = "交易复盘")
    @GetMapping("/tradeReview")
    public ServiceResult<List<Map<String, Object>>> tradeReview(@Valid SpotTradeElementCurveReqVo reqVo) {
        return spotTradeElementCurveService.tradeReview(reqVo);
    }

    //交易复盘日96点数据
    @Operation(summary = "交易复盘96点数据")
    @GetMapping("/tradeReviewCurve")
    public ServiceResult<List<SpotTradeElementCurveListRespVo>> tradeReviewCurve(@Valid SpotTradeElementCurveReqVo reqVo) {
        return spotTradeElementCurveService.tradeReviewCurve(reqVo);
    }

}
