package com.bobandata.cloud.trade.elec.controller.dayRolling.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.sql.Date;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:24
 * @description
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@Schema(description = "中长期记录 Resp VO")
public class SpotTradeDayRollingListRespVo {

    @Schema(description = "User name")
    private String userName;

    @Schema(description = "Unit name")
    private String unitName;

    @Schema(description = "Unit ID")
    private String unitId;

    @Schema(description = "Unit type")
    private String unitType;

    @Schema(description = "Alias of the station")
    private String aliasStation;

    @Schema(description = "Alias of the unit")
    private String aliasUnit;

    @Schema(description = "Declaration date")
    private Date declareDate;

    @Schema(description = "Biaodi date")
    private Date biaodiDate;

    @Schema(description = "Trade name")
    private String tradeName;

    @Schema(description = "Part name")
    private String partName;

    @Schema(description = "Report type")
    private String reportType;

    @Schema(description = "Trade role")
    private Integer tradeRole;

    @Schema(description = "Period self deal price")
    private Double periodSelfDealPrice;

    @Schema(description = "Period self deal amount")
    private Double periodSelfDealAmount;
}
