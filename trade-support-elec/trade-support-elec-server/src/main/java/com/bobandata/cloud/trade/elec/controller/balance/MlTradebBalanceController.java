package com.bobandata.cloud.trade.elec.controller.balance;

import com.bobandata.cloud.trade.elec.controller.balance.vo.MlTradeBalanceListRespVo;
import com.bobandata.cloud.trade.elec.controller.balance.vo.MlTradeBalanceReqVo;
import com.bobandata.cloud.trade.elec.dal.dataobject.balance.MlTradeBalance;
import com.bobandata.cloud.trade.elec.dal.mysql.balance.MlTradeBalanceMapper;
import com.bobandata.cloud.trade.elec.service.balance.MlTradeBalanceService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


@Tag(name = "交易中长期合同最终交易结果")
@Slf4j
@RequestMapping("/mlTradeBalance")
@RestController
public class MlTradebBalanceController extends BaseCrudRestController<MlTradeBalance, MlTradeBalanceMapper> {

    @Autowired
    private MlTradeBalanceService mlTradeBalanceService;


    @Operation(summary = "中长期list")
    @GetMapping("/list")
    public PagingResult<MlTradeBalanceListRespVo> listRecord(@Valid MlTradeBalanceReqVo reqVo) {
        return mlTradeBalanceService.listRecord(reqVo);
    }

}
