package com.bobandata.cloud.trade.elec.controller.spotTrade;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.elec.controller.spotTrade.vo.ElementNewenergyForeListRespVo;
import com.bobandata.cloud.trade.elec.controller.spotTrade.vo.ElementNewenergyForeReqVo;
import com.bobandata.cloud.trade.elec.service.spotTrade.ElementNewenergyForeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Tag(name = "现货交易 - 新能源元素预测")
@Slf4j
@RequestMapping("/spotTrade/elementNewenergyFore") // 路径调整
@RestController
@Validated
public class ElementNewenergyForeController {

    @Autowired
    private ElementNewenergyForeService elementNewenergyForeService;

    @Operation(summary = "分页查询列表")
    @GetMapping("/list")
    public ServiceResult<List<ElementNewenergyForeListRespVo>> list(@Valid ElementNewenergyForeReqVo reqVo) {
        return elementNewenergyForeService.list(reqVo);
    }

} 