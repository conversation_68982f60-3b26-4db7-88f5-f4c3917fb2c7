package com.bobandata.cloud.trade.elec.controller.settle;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.elec.controller.settle.vo.SettleUnitDetailListRespVo;
import com.bobandata.cloud.trade.elec.controller.settle.vo.SettleUnitDetailReqVo;
import com.bobandata.cloud.trade.elec.dal.dataobject.settle.SettleUnitDetail;
import com.bobandata.cloud.trade.elec.service.settle.SettleUnitDetailService;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Tag(name = "结算管理 - 结算单元明细")
@Slf4j
@RequestMapping("/settleUnitDetail")
@RestController
@Validated // 开启方法级别的参数校验
public class SettleUnitDetailController {
    // 不再继承 BaseCrudRestController，因为 Service 方法名已自定义

    @Autowired
    private SettleUnitDetailService settleUnitDetailService;

    @Operation(summary = "分页查询列表")
    @GetMapping("/list")
    public PagingResult<SettleUnitDetailListRespVo> list(@Valid SettleUnitDetailReqVo reqVo) {
        return settleUnitDetailService.list(reqVo);
    }

    @Operation(summary = "新增")
    @PostMapping("/insert")
    public ServiceResult<Long> insert(@RequestBody @Valid SettleUnitDetail settleUnitDetail) {
        // 假设新增时直接传入 SettleUnitDetail 对象
        // 如果需要 CreateReqVo，需添加并使用 Convert 转换
        return settleUnitDetailService.insertDetail(settleUnitDetail);
    }

    @Operation(summary = "修改")
    @PostMapping("/update")
    public ServiceResult<Boolean> update(@RequestBody @Valid SettleUnitDetail settleUnitDetail) {
        // 假设修改时直接传入 SettleUnitDetail 对象，且包含 ID
        // 如果需要 UpdateReqVo，需添加并使用 Convert 转换
        return settleUnitDetailService.updateDetail(settleUnitDetail);
    }

    @Operation(summary = "删除")
    @GetMapping("/delete") // 沿用 GET 进行删除
    public ServiceResult<Boolean> delete(@Parameter(description = "主键ID", required = true) @RequestParam("id") Long id) {
        return settleUnitDetailService.deleteDetail(id);
    }

    @Operation(summary = "获取单个详情")
    @GetMapping("/get")
    public ServiceResult<SettleUnitDetail> get(@Parameter(description = "主键ID", required = true) @RequestParam("id") Long id) {
        // IService 提供了 getById 方法
        SettleUnitDetail entity = settleUnitDetailService.getById(id);
        // 可以根据需要判断是否为 null 并返回不同结果
        return ServiceResult.success(entity);
    }

} 