package com.bobandata.cloud.trade.elec.controller.spotTrade.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "新能源元素预测数据 - 列表 Response VO")
@Data
public class ElementNewenergyForeListRespVo {

    @Schema(description = "P1值")
    private BigDecimal p1;

    @Schema(description = "P2值")
    private BigDecimal p2;

    @Schema(description = "P3值")
    private BigDecimal p3;

    @Schema(description = "P4值")
    private BigDecimal p4;

    @Schema(description = "P5值")
    private BigDecimal p5;
    @Schema(description = "P6值")
    private BigDecimal p6;

    @Schema(description = "P7值")
    private BigDecimal p7;

    @Schema(description = "P8值")
    private BigDecimal p8;

    @Schema(description = "P9值")
    private BigDecimal p9;

    @Schema(description = "P10值")
    private BigDecimal p10;

    @Schema(description = "P11值")
    private BigDecimal p11;

    @Schema(description = "P12值")
    private BigDecimal p12;

    @Schema(description = "P13值")
    private BigDecimal p13;

    @Schema(description = "P14值")
    private BigDecimal p14;

    @Schema(description = "P15值")
    private BigDecimal p15;

    @Schema(description = "P16值")
    private BigDecimal p16;

    @Schema(description = "P17值")
    private BigDecimal p17;

    @Schema(description = "P18值")
    private BigDecimal p18;

    @Schema(description = "P19值")
    private BigDecimal p19;

    @Schema(description = "P20值")
    private BigDecimal p20;

    @Schema(description = "P21值")
    private BigDecimal p21;

    @Schema(description = "P22值")
    private BigDecimal p22;

    @Schema(description = "P23值")
    private BigDecimal p23;

    @Schema(description = "P24值")
    private BigDecimal p24;

    @Schema(description = "P25值")
    private BigDecimal p25;

    @Schema(description = "P26值")
    private BigDecimal p26;

    @Schema(description = "P27值")
    private BigDecimal p27;

    @Schema(description = "P28值")
    private BigDecimal p28;

    @Schema(description = "P29值")
    private BigDecimal p29;

    @Schema(description = "P30值")
    private BigDecimal p30;

    @Schema(description = "P31值")
    private BigDecimal p31;

    @Schema(description = "P32值")
    private BigDecimal p32;

    @Schema(description = "P33值")
    private BigDecimal p33;

    @Schema(description = "P34值")
    private BigDecimal p34;

    @Schema(description = "P35值")
    private BigDecimal p35;

    @Schema(description = "P36值")
    private BigDecimal p36;

    @Schema(description = "P37值")
    private BigDecimal p37;

    @Schema(description = "P38值")
    private BigDecimal p38;

    @Schema(description = "P39值")
    private BigDecimal p39;

    @Schema(description = "P40值")
    private BigDecimal p40;

    @Schema(description = "P41值")
    private BigDecimal p41;

    @Schema(description = "P42值")
    private BigDecimal p42;

    @Schema(description = "P43值")
    private BigDecimal p43;

    @Schema(description = "P44值")
    private BigDecimal p44;

    @Schema(description = "P45值")
    private BigDecimal p45;

    @Schema(description = "P46值")
    private BigDecimal p46;

    @Schema(description = "P47值")
    private BigDecimal p47;

    @Schema(description = "P48值")
    private BigDecimal p48;

    @Schema(description = "P49值")
    private BigDecimal p49;

    @Schema(description = "P50值")
    private BigDecimal p50;

    @Schema(description = "P51值")
    private BigDecimal p51;

    @Schema(description = "P52值")
    private BigDecimal p52;

    @Schema(description = "P53值")
    private BigDecimal p53;

    @Schema(description = "P54值")
    private BigDecimal p54;

    @Schema(description = "P55值")
    private BigDecimal p55;

    @Schema(description = "P56值")
    private BigDecimal p56;

    @Schema(description = "P57值")
    private BigDecimal p57;

    @Schema(description = "P58值")
    private BigDecimal p58;

    @Schema(description = "P59值")
    private BigDecimal p59;

    @Schema(description = "P60值")
    private BigDecimal p60;

    @Schema(description = "P61值")
    private BigDecimal p61;

    @Schema(description = "P62值")
    private BigDecimal p62;

    @Schema(description = "P63值")
    private BigDecimal p63;

    @Schema(description = "P64值")
    private BigDecimal p64;

    @Schema(description = "P65值")
    private BigDecimal p65;

    @Schema(description = "P66值")
    private BigDecimal p66;

    @Schema(description = "P67值")
    private BigDecimal p67;

    @Schema(description = "P68值")
    private BigDecimal p68;

    @Schema(description = "P69值")
    private BigDecimal p69;

    @Schema(description = "P70值")
    private BigDecimal p70;

    @Schema(description = "P71值")
    private BigDecimal p71;

    @Schema(description = "P72值")
    private BigDecimal p72;

    @Schema(description = "P73值")
    private BigDecimal p73;

    @Schema(description = "P74值")
    private BigDecimal p74;

    @Schema(description = "P75值")
    private BigDecimal p75;

    @Schema(description = "P76值")
    private BigDecimal p76;

    @Schema(description = "P77值")
    private BigDecimal p77;

    @Schema(description = "P78值")
    private BigDecimal p78;

    @Schema(description = "P79值")
    private BigDecimal p79;

    @Schema(description = "P80值")
    private BigDecimal p80;

    @Schema(description = "P81值")
    private BigDecimal p81;

    @Schema(description = "P82值")
    private BigDecimal p82;

    @Schema(description = "P83值")
    private BigDecimal p83;

    @Schema(description = "P84值")
    private BigDecimal p84;

    @Schema(description = "P85值")
    private BigDecimal p85;

    @Schema(description = "P86值")
    private BigDecimal p86;

    @Schema(description = "P87值")
    private BigDecimal p87;

    @Schema(description = "P88值")
    private BigDecimal p88;

    @Schema(description = "P89值")
    private BigDecimal p89;

    @Schema(description = "P90值")
    private BigDecimal p90;

    @Schema(description = "P91值")
    private BigDecimal p91;

    @Schema(description = "P92值")
    private BigDecimal p92;

    @Schema(description = "P93值")
    private BigDecimal p93;

    @Schema(description = "P94值")
    private BigDecimal p94;

    @Schema(description = "P95值")
    private BigDecimal p95;

    @Schema(description = "P96值")
    private BigDecimal p96;

} 