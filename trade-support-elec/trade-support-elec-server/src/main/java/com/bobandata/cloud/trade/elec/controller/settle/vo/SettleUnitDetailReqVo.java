package com.bobandata.cloud.trade.elec.controller.settle.vo;

import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "结算单元明细 - 分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SettleUnitDetailReqVo extends PageParam {

    @Schema(description = "单元编码", example = "UNIT_001")
    private String unitCode;

    @Schema(description = "数据时间 (格式 YYYY-MM-DD or YYYY-MM-DD HH:mm:ss)", example = "2024-07-30")
    private String dataTime;

    @Schema(description = "结算属性", example = "实时结算")
    private String settleAttribute;

} 